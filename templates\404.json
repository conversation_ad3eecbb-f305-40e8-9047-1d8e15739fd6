/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "main": {
      "type": "main-404",
      "blocks": {
        "text_GqmVFf": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "<h1>Page not found</h1>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "h2",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "text_Ua9XAW": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "<p>The link may be incorrect, or the page has been removed. </p>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 16,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "button_PAQMwR": {
          "type": "button",
          "name": "t:names.button",
          "settings": {
            "label": "Continue shopping",
            "link": "shopify://collections/all",
            "open_in_new_tab": false,
            "style_class": "button",
            "width": "fit-content",
            "custom_width": 100,
            "width_mobile": "fit-content",
            "custom_width_mobile": 100
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_GqmVFf",
        "text_Ua9XAW",
        "button_PAQMwR"
      ],
      "settings": {
        "content_direction": "column",
        "section_width": "page-width",
        "section_height": "small",
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 20,
        "color_scheme": "",
        "padding-block-start": 100,
        "padding-block-end": 100
      }
    },
    "product_list_rEkUNj": {
      "type": "product-list",
      "blocks": {
        "static-header": {
          "type": "_product-list-content",
          "name": "t:names.header",
          "static": true,
          "settings": {
            "content_direction": "row",
            "vertical_on_mobile": false,
            "horizontal_alignment": "space-between",
            "vertical_alignment": "flex-end",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_list_text_BrfRNU": {
              "type": "_product-list-text",
              "name": "t:names.collection_title",
              "settings": {
                "text": "<h3>Discover something new</h3>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h3",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "collection_link_button_x6FBXn": {
              "type": "_product-list-button",
              "name": "t:names.product_list_button",
              "settings": {
                "label": "SHOP ALL",
                "open_in_new_tab": false,
                "style_class": "link",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 50
              },
              "blocks": {}
            }
          },
          "block_order": [
            "product_list_text_BrfRNU",
            "collection_link_button_x6FBXn"
          ]
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {
            "product_card_gap": 4,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_card_gallery_eqacrx": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media",
              "settings": {
                "image_ratio": "portrait",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group_NYhiKM": {
              "type": "_product-card-group",
              "settings": {
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": false,
                "horizontal_alignment": "space-between",
                "vertical_alignment": "flex-start",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "flex-start",
                "gap": 4,
                "width": "fill",
                "custom_width": 35,
                "width_mobile": "fill",
                "custom_width_mobile": 43,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "group_dnEeFk": {
                  "type": "_product-card-group",
                  "name": "t:names.group",
                  "settings": {
                    "open_in_new_tab": false,
                    "content_direction": "column",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "center",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "flex-start",
                    "gap": 4,
                    "width": "fill",
                    "custom_width": 40,
                    "width_mobile": "fit-content",
                    "custom_width_mobile": 20,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 8,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "product_title_nN6yqL": {
                      "type": "product-title",
                      "name": "t:names.product_title",
                      "settings": {
                        "width": "fit-content",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h5",
                        "font": "var(--font-body--family)",
                        "font_size": "1rem",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "var(--color-foreground)",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "price_fViDQC": {
                      "type": "price",
                      "settings": {
                        "show_sale_price_first": true,
                        "show_installments": false,
                        "show_tax_info": false,
                        "type_preset": "h6",
                        "width": "100%",
                        "alignment": "left",
                        "font": "var(--font-body--family)",
                        "font_size": "1rem",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "color": "var(--color-foreground)",
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    }
                  },
                  "block_order": [
                    "product_title_nN6yqL",
                    "price_fViDQC"
                  ]
                },
                "swatches_89wYFx": {
                  "type": "swatches",
                  "settings": {
                    "product_swatches_alignment": "flex-start",
                    "product_swatches_alignment_mobile": "flex-start",
                    "hide_padding": false,
                    "product_swatches_padding_top": 4,
                    "product_swatches_padding_bottom": 10,
                    "product_swatches_padding_left": 0,
                    "product_swatches_padding_right": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "group_dnEeFk",
                "swatches_89wYFx"
              ]
            }
          },
          "block_order": [
            "product_card_gallery_eqacrx",
            "group_NYhiKM"
          ]
        }
      },
      "name": "Featured collection",
      "settings": {
        "collection": "all",
        "layout_type": "grid",
        "carousel_on_mobile": false,
        "max_products": 4,
        "columns": 4,
        "mobile_columns": "2",
        "mobile_card_size": "60cqw",
        "columns_gap": 12,
        "rows_gap": 12,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "horizontal_alignment": "flex-start",
        "gap": 24,
        "color_scheme": "scheme-1",
        "padding-block-start": 70,
        "padding-block-end": 36
      }
    }
  },
  "order": [
    "main",
    "product_list_rEkUNj"
  ]
}