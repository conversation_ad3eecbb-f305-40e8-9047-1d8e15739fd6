{"names": {"404": "404", "borders": "Obramowania", "collapsible_row": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "custom_section": "<PERSON><PERSON><PERSON>ja <PERSON>rdo<PERSON>", "icon": "<PERSON><PERSON><PERSON>", "logo_and_favicon": "Logo i ikona Favicon", "product_buy_buttons": "Przyciski zakupu", "product_description": "Opis", "product_price": "<PERSON><PERSON>", "slideshow": "Pokaz slajdów", "typography": "Typografia", "video": "Film", "colors": "<PERSON><PERSON><PERSON>", "overlapping_blocks": "Nakładające się bloki", "product_variant_picker": "Selektor wariantów", "slideshow_controls": "Elementy sterujące pokazem slajdów", "size": "Rozmiar", "spacing": "Odstępy", "product_recommendations": "Polecane produkty", "product_media": "Multimedia produktu", "featured_collection": "<PERSON><PERSON><PERSON>", "add_to_cart": "Dodaj do koszyka", "email_signup": "Rejestracja w celu otrzymywania e-maili", "submit_button": "Przycisk Prześlij", "grid_layout_selector": "Se<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "list_items": "<PERSON><PERSON><PERSON><PERSON>y", "facets": "Aspekty", "variants": "Warianty", "styles": "Style", "product_cards": "Karty produktów", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": "<PERSON>", "primary_button": "Przycisk główny", "secondary_button": "Przycisk dodatkowy", "popovers": "Wyskakujące okienka", "marquee": "<PERSON><PERSON>", "alternating_content_rows": "Na<PERSON>rz<PERSON><PERSON>", "pull_quote": "Cytat typu pull", "contact_form": "<PERSON><PERSON>", "featured_product": "Główne cechy produktu", "icons_with_text": "Ikony z tekstem", "product_list": "<PERSON><PERSON><PERSON>", "spacer": "Rozdzielacz", "products_carousel": "Polecana kolekcja: ka<PERSON><PERSON><PERSON>", "products_grid": "Polecana kolekcja: siatka", "accelerated_checkout": "Przyspieszona realizacja zakupu", "accordion": "Akordeon", "accordion_row": "R<PERSON><PERSON><PERSON>", "animations": "Animacje", "announcement": "Ogłos<PERSON><PERSON>", "announcement_bar": "Pasek <PERSON>ł<PERSON>zeń", "badges": "Znaczki", "button": "Przycisk", "cart": "<PERSON><PERSON><PERSON>", "cart_items": "Pozycje w koszyku", "cart_products": "Produkty w koszyku", "cart_title": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_card": "<PERSON><PERSON><PERSON>", "collection_columns": "<PERSON><PERSON><PERSON> k<PERSON>", "collection_container": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_description": "Opisy k<PERSON>ji", "collection_image": "<PERSON><PERSON><PERSON>", "collection_info": "Informacje o kolekcji", "collection_list": "Lista kolekcji", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON>", "content_grid": "Siatka treści", "details": "Szczegóły", "divider": "Separator", "filters": "Filtrowanie i sortowanie", "follow_on_shop": "<PERSON><PERSON>er<PERSON><PERSON> w Shop", "footer": "Stopka", "footer_utilities": "Narzędzia stopki", "group": "Grupa", "header": "Nagłówek", "heading": "Nagłówek", "icons": "<PERSON><PERSON><PERSON>", "image_with_text": "Obraz z tekstem", "input": "<PERSON>", "logo": "Logo", "magazine_grid": "Siatka czasopisma", "media": "Multimedia", "menu": "<PERSON><PERSON>", "mobile_layout": "Układ na urządzeniu mobilnym", "payment_icons": "Ikon<PERSON>", "popup_link": "Wyskakujący link", "predictive_search": "Wyskakujące okienko wyszukiwania", "predictive_search_empty": "Zapytania wyszukiwania z podpowiedziami są puste", "price": "<PERSON><PERSON>", "product": "Produkt", "product_card": "Karta produktów", "product_card_media": "Multimedia", "product_card_rendering": "Render karty produktów", "product_grid": "Siatka", "product_grid_main": "Siatka produktów", "product_image": "Obraz produktu", "product_information": "Informacje o produkcie", "product_review_stars": "Gwiazdki recenzji", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "row": "<PERSON><PERSON><PERSON>", "search": "Wyszukiwanie", "section": "<PERSON><PERSON><PERSON><PERSON>", "selected_variants": "<PERSON><PERSON><PERSON><PERSON> war<PERSON>y", "shop_the_look": "<PERSON><PERSON>", "slide": "<PERSON><PERSON><PERSON><PERSON>", "social_media_links": "Linki do mediów społecznościowych", "steps": "<PERSON><PERSON><PERSON>", "summary": "Podsumowanie", "swatches": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testimonials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "Tekst", "title": "<PERSON><PERSON><PERSON>", "utilities": "Użyteczne funkcje", "search_input": "Wpis wys<PERSON>ia", "search_results": "Wyniki wyszukiwania", "read_only": "Tylko do odczytu", "collection_title": "<PERSON><PERSON><PERSON>", "collections_bento": "Lista kolekcji: <PERSON><PERSON>", "faq_section": "Często zadawane pytanie", "hero": "<PERSON><PERSON><PERSON>", "jumbo_text": "Tekst jumbo", "view_all_button": "Wyświetl wszystko", "video_section": "Film", "custom_liquid": "Niestandardowy kod Liquid", "blog": "Blog", "blog_post": "Post na blogu", "blog_posts": "Posty na blogu", "caption": "<PERSON><PERSON>", "collection_card_image": "<PERSON><PERSON><PERSON>", "collection_links": "Linki do kolekcji", "collection_links_spotlight": "<PERSON><PERSON>: Spotlight", "collection_links_text": "Linki fo kolekcji: tekst", "collections_carousel": "Lista kolekcji: ka<PERSON><PERSON><PERSON>", "collections_editorial": "Lista kolekcji: <PERSON><PERSON><PERSON>ak<PERSON>y", "collections_grid": "Lista kolekcji: siatka", "copyright": "Prawo autorskie", "count": "Liczba", "divider_section": "Separator", "drawers": "Szuflady", "editorial": "<PERSON><PERSON><PERSON><PERSON>", "editorial_jumbo_text": "Artykuł: tekst jumbo", "hero_marquee": "Element główny: baner", "input_fields": "Pola wprowadzania", "local_pickup": "Odbiór lo<PERSON>ny", "marquee_section": "<PERSON><PERSON>", "media_with_text": "Media z tekstem", "page": "Strona", "page_content": "<PERSON><PERSON><PERSON><PERSON>", "page_layout": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>y", "policy_list": "Linki do polityki", "prices": "<PERSON><PERSON>", "products_editorial": "Polecana kolekcja: mater<PERSON><PERSON> redak<PERSON>y", "social_link": "Link do mediów społecznościowych", "split_showcase": "Podzielona prezentacja", "variant_pickers": "Selektory wariantów", "product_title": "Tytuł produktu", "large_logo": "Duże logo", "product_list_button": "Przycisk Wyświetl wszystkie", "product_inventory": "Zapas produktu", "pills": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Opis", "featured_image": "Wyróżniony obraz"}, "settings": {"autoplay": "Autoodtwarzanie", "background": "Tło", "border_radius": "Promień rogu", "border_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>mowan<PERSON>", "borders": "Obramowania", "bottom_padding": "Wypełnienie na dole", "color": "<PERSON><PERSON>", "content_direction": "Kierunek zawartości", "content_position": "Położenie zawartości", "cover_image_size": "<PERSON><PERSON><PERSON><PERSON><PERSON> obrazu w tle", "cover_image": "<PERSON><PERSON><PERSON> w tle", "custom_width": "Niestandardowa szerokość", "enable_video_looping": "Zapętlanie wideo", "favicon": "<PERSON>kon<PERSON> Favicon", "heading": "Nagłówek", "icon": "<PERSON><PERSON><PERSON>", "image_icon": "<PERSON><PERSON><PERSON> o<PERSON>", "make_section_full_width": "Rozwiń sekcję na całą szerokość", "overlay_opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "padding": "Wypełnienie", "product": "Produkt", "text": "Tekst", "top_padding": "Wypełnienie na górze", "video": "Film", "video_alt_text": "Alternatywny tekst", "video_loop": "Zapętlony film", "video_position": "Pozycja filmu", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignment": "Wyrównanie", "button": "Przycisk", "colors": "<PERSON><PERSON><PERSON>", "content_alignment": "Wyrównanie zawartości", "custom_minimum_height": "Niestandardowa minimalna w<PERSON>ć", "font_family": "<PERSON><PERSON><PERSON>", "gap": "Odstęp", "geometric_translate_y": "Przesunięcie geometryczne wzdłuż osi Y", "image": "<PERSON><PERSON><PERSON>", "image_opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obrazu", "image_position": "Położenie obrazu", "image_ratio": "Proporcja obrazu", "label": "Etykieta", "line_height": "<PERSON><PERSON><PERSON><PERSON><PERSON> linii", "link": "Link", "layout_gap": "Luka w układzie", "minimum_height": "Minimal<PERSON>", "opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_color": "<PERSON><PERSON>", "section_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekcji", "size": "Rozmiar", "slide_spacing": "Luka w slajdzie", "slide_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> slajdu", "slideshow_fullwidth": "Slajdy o pełnej szerokości", "style": "<PERSON><PERSON>", "text_case": "<PERSON><PERSON><PERSON><PERSON><PERSON> liter", "z_index": "<PERSON><PERSON><PERSON>", "limit_content_width": "<PERSON><PERSON><PERSON><PERSON> treści", "color_scheme": "Kolorystyka", "inherit_color_scheme": "Kolorystyka", "product_count": "Liczba produktów", "product_type": "Typ produktu", "content_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> treści", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable_sticky_content": "Włącz przypiętą zawartość na komputerze", "error_color": "Błąd", "success_color": "Powodzenie", "primary_font": "Czcionka podstawowa", "secondary_font": "Czcionka dodatkowa", "tertiary_font": "Czcionka trzeciorzędna", "columns": "<PERSON><PERSON><PERSON>", "items_to_show": "Pozycje do wyświetlenia", "layout": "<PERSON><PERSON><PERSON><PERSON>", "layout_type": "<PERSON><PERSON>", "show_grid_layout_selector": "Wyświetl selektor układu <PERSON>atki", "view_more_show": "Pokaż przycisk „Wyświetl wszystkie”", "image_gap": "Odstęp między obrazami", "width_desktop": "Szerokość na komputerze", "width_mobile": "Szerokość na urządzeniu mobilnym", "border_style": "Styl obramowania", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thickness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke": "<PERSON><PERSON><PERSON><PERSON>", "filter_style": "<PERSON><PERSON><PERSON><PERSON> styl", "swatches": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quick_add_colors": "Szybkie dodawanie kolorów", "divider_color": "Separator", "border_opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obramowania", "hover_background": "Tło obszaru aktywnego", "hover_borders": "Obramowanie obszaru aktywnego", "hover_text": "Tekst obszaru aktywnego", "primary_hover_color": "Linki obszaru aktywnego", "primary_button_text": "Tekst głównego przycisku", "primary_button_background": "Tło głównego przycisku", "primary_button_border": "Obramowanie przycisku głównego", "secondary_button_text": "Tekst przycisku dodatkowego", "secondary_button_background": "Tło przycisku dodatkowego", "secondary_button_border": "Obramowanie przycisku dodatkowego", "shadow_color": "Cień", "video_autoplay": "Autoodtwarzanie", "video_cover_image": "<PERSON><PERSON><PERSON>", "video_external_url": "Adres URL", "video_source": "Źródło", "background_color": "<PERSON><PERSON>", "first_row_media_position": "Pozycja multimediów w pierwszym wierszu", "hide_padding": "<PERSON><PERSON><PERSON><PERSON>", "size_mobile": "Rozmiar mobilny", "pixel_size_mobile": "Rozmiar w pikselach", "percent_size_mobile": "Rozmiar w procentach", "unit": "Jednostka", "custom_mobile_size": "Niestandardowy rozmiar mobilny", "fixed_height": "Wysokość w pikselach", "fixed_width": "Szerokość w pikselach", "percent_height": "Wysokość w procentach", "percent_width": "Szerokość w procentach", "percent_size": "Rozmiar w procentach", "pixel_size": "Rozmiar w pikselach", "card_image_height": "<PERSON><PERSON><PERSON><PERSON><PERSON> obrazu produktu", "logo_font": "Czcionka logo", "accordion": "Akordeon", "aspect_ratio": "Współczynnik proporcji", "auto_rotate_announcements": "Automatyczna zmiana ogłoszeń", "auto_rotate_slides": "Automatyczna zmiana slajdów", "badge_corner_radius": "Promień rogu", "badge_position": "Położenie na kartach", "badge_sale_color_scheme": "W promocji", "badge_sold_out_color_scheme": "Wyprzedane", "behavior": "Zachowanie", "blur": "Rozmycie cienia", "border": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bottom": "<PERSON><PERSON><PERSON>", "carousel_on_mobile": "Karuzela na urządzenia mobilne", "cart_count": "Liczba produktów w koszyku", "cart_items": "Pozycje w koszyku", "cart_related_products": "Powiązane produkty", "cart_title": "<PERSON><PERSON><PERSON>", "cart_total": "Całkowita wartość koszyka", "cart_type": "<PERSON><PERSON>", "case": "<PERSON><PERSON><PERSON><PERSON><PERSON> liter", "checkout_buttons": "Przyciski przyspieszonej realizacji zakupu", "collection_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_templates": "Szablony kolekcji", "content": "<PERSON><PERSON><PERSON><PERSON>", "corner_radius": "Promień rogu", "country_region": "Kraj/region", "currency_code": "<PERSON><PERSON> wa<PERSON>y", "custom_height": "Niestandardowa wysokość", "desktop_height": "Szerokość na komputerze", "direction": "<PERSON><PERSON><PERSON><PERSON>", "display": "Wyś<PERSON><PERSON>l", "divider_thickness": "<PERSON><PERSON><PERSON><PERSON><PERSON> separatora", "divider": "Separator", "dividers": "Separatory", "drop_shadow": "Rzuć cień", "empty_state_collection_info": "Wyświetlane przed wprowadzeniem wyszukiwania", "empty_state_collection": "Status pustej kolekcji", "enable_filtering": "Filtry", "enable_grid_density": "<PERSON><PERSON><PERSON><PERSON>", "enable_sorting": "Sort<PERSON>nie", "enable_zoom": "Włącz powiększenie", "equal_columns": "Równe kolumny", "expand_first_group": "Rozwiń pierwszą grupę", "extend_media_to_screen_edge": "Rozciągnij multimedia do krawędzi ekranu", "extend_summary": "Rozciągnij do krawędzi ekranu", "extra_large": "<PERSON><PERSON><PERSON>", "extra_small": "<PERSON><PERSON><PERSON>", "flag": "Flag<PERSON>", "font_price": "Czcionka ceny", "font_weight": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "font": "Czcionka", "full_width_first_image": "Pierwszy obraz o pełnej szerokości", "full_width_on_mobile": "Pełna szerokość na urządzeniu mobilnym", "heading_preset": "Ustawienie wstępne nagłówka", "hide_unselected_variant_media": "Ukryj niewybrane pliki multimedialne wariantów", "horizontal_gap": "Odstęp w poziomie", "horizontal_offset": "Poziome przesunięcie cienia", "hover_behavior": "Zachowanie po najechaniu kursorem", "icon_background": "<PERSON><PERSON><PERSON>", "icons": "<PERSON><PERSON><PERSON>", "image_border_radius": "Promień rogu obrazu", "installments": "<PERSON><PERSON>", "integrated_button": "Zintegrowany przycisk", "language_selector": "Selektor <PERSON>zy<PERSON>", "large": "<PERSON><PERSON><PERSON>", "left_padding": "Wypełnienie z lewej", "left": "<PERSON><PERSON> strona", "letter_spacing": "Odstęp między literami", "limit_media_to_screen_height": "Ograniczenie do wysokości ekranu", "limit_product_details_width": "Ogra<PERSON><PERSON> szer<PERSON>ć szczegółów produktu", "link_preset": "Ustawienie wstępne linku", "links": "<PERSON><PERSON>", "logo": "Logo", "loop": "Pętla", "make_details_sticky_desktop": "Przyklejony na komputerach", "max_width": "<PERSON><PERSON><PERSON>", "media_height": "Wysokość multimediów", "media_overlay": "Nakładka na multimediach", "media_position": "Pozycja multimediów", "media_type": "Typ multimediów", "media_width": "Szerokość multimediów", "menu": "<PERSON><PERSON>", "mobile_columns": "Kolumny na urządzeniu mobilnym", "mobile_height": "Wysokość na urządzeniu mobilnym", "mobile_logo_image": "Logo na urządzeniu mobilnym", "mobile_quick_add": "Szybkie dodawanie na urządzeniu mobilnym", "motion_direction": "<PERSON><PERSON><PERSON><PERSON> ruchu", "motion": "<PERSON><PERSON>", "movement_direction": "<PERSON><PERSON><PERSON><PERSON> ruchu", "navigation_bar_color_scheme": "Schemat kolorów paska nawigacji", "navigation_bar": "Pasek <PERSON>", "navigation": "<PERSON><PERSON><PERSON><PERSON>", "open_new_tab": "Otwórz link w nowej karcie", "overlay_color": "<PERSON><PERSON>", "overlay": "Nakładka", "padding_bottom": "Wypełnienie na dole", "padding_horizontal": "Wypełnienie w poziomie", "padding_top": "Wypełnienie na górze", "page_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> strony", "pagination": "<PERSON>gin<PERSON><PERSON>", "placement": "Umieszczanie", "position": "<PERSON><PERSON><PERSON>ja", "preset": "Ustawienie wstępne", "product_cards": "Karty produktów", "product_pages": "Strony produktu", "product_templates": "Szablony produktów", "products": "Produkty", "quick_add": "Szybkie dodawanie", "ratio": "Propor<PERSON>ja", "regular": "Zwykły", "review_count": "Liczba recenzji", "right": "<PERSON><PERSON><PERSON> strona", "row_height": "W<PERSON>okość rzędu", "row": "<PERSON><PERSON><PERSON>", "seller_note": "Zezwalaj na dodawanie uwag dla sprzedawcy", "shape": "Kształt", "show_as_accordion": "Wyświetlaj jako akordeon na urządzeniach mobilnych", "show_sale_price_first": "Najpierw pokaż cenę promocyjną", "show_tax_info": "Informacje podatkowe", "show": "Po<PERSON><PERSON>", "small": "<PERSON><PERSON>", "speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statement": "Wyciąg", "sticky_header": "Przyklejony nagłówek", "text_hierarchy": "Hierarchia tekstu", "text_presets": "Ustawienia wstępne tekstu", "title": "<PERSON><PERSON><PERSON>", "top": "Góra", "type": "<PERSON><PERSON>", "type_preset": "Ustawienie wstępne tekstu", "underline_thickness": "<PERSON><PERSON><PERSON><PERSON><PERSON> podkreślenia", "variant_images": "<PERSON><PERSON><PERSON>", "vendor": "Dostawca", "vertical_gap": "Odstęp w pionie", "vertical_offset": "Przesunięcie cienia w pionie", "vertical_on_mobile": "Pionowo na urządzeniu mobilnym", "view_all_as_last_card": "„Wyświetl wszystkie” jako ostatnia karta", "weight": "W<PERSON>", "wrap": "<PERSON><PERSON><PERSON><PERSON>", "read_only": "Tylko do odczytu", "always_stack_buttons": "Zawsze układaj przyciski", "custom_mobile_width": "Niestandardowa szerokość na urządzeniu mobilnym", "gradient_direction": "<PERSON><PERSON><PERSON><PERSON>", "overlay_style": "<PERSON><PERSON>", "shadow_opacity": "<PERSON><PERSON><PERSON> cien<PERSON>", "show_filter_label": "Etykiety tekstowe dla zastosowanych filtrów", "show_swatch_label": "Etykiety tekstowe dla próbek", "transparent_background": "Przezroczyste tło", "account": "Ko<PERSON>", "align_baseline": "Wyrównaj linię bazową tekstu", "add_discount_code": "Włącz rabaty w koszyku", "background_overlay": "Nakładka tła", "background_media": "Multimedia w tle", "border_thickness": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>mowan<PERSON>", "bottom_row": "Wiersz dolny", "button_text_case": "Wielkość liter tekstu", "button_text_weight": "Waga tekstu", "auto_open_cart_drawer": "„Dodaj do koszyka” automatycznie otwiera szufladę", "collection_count": "Liczba kolekcji", "custom_liquid": "Kod Liquid", "default": "Domy<PERSON><PERSON><PERSON>", "default_logo": "Domyślne logo", "divider_width": "Szerokość separatora", "headings": "Nagłówki", "hide_logo_on_home_page": "Ukryj logo na stronie głównej", "horizontal_padding": "Wypełnienie poziome", "inverse": "Odwrócenie", "inverse_logo": "Odwrócone logo", "layout_style": "<PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile_pagination": "Paginacja mobilna", "open_row_by_default": "Domyślnie otwórz wiersz", "page_transition_enabled": "Przejścia między stronami", "search": "Wyszukiwanie", "search_icon": "Ikona wyszukiwania", "search_position": "<PERSON><PERSON><PERSON>ja", "search_row": "<PERSON><PERSON><PERSON>", "show_author": "Autor", "show_alignment": "Pokaż dopasowanie", "show_count": "Pokaż liczbę", "show_date": "Data", "show_pickup_availability": "Pokaż możliwość odbioru", "show_search": "Pokaż wyszukiwanie", "use_inverse_logo": "Użyj odwróconego logo", "vertical_padding": "Wypełnienie pionowe", "visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product_corner_radius": "Promień rogu produktu", "card_corner_radius": "Promień rogu karty", "alignment_mobile": "Dopasowanie na urządzeniu mobilnym", "animation_repeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "blurred_reflection": "Zamazane odbicie", "card_hover_effect": "Efekt najechania kursorem na kartę", "card_size": "<PERSON>oz<PERSON>r karty", "collection_title_case": "Wielkość liter tytułu kolekcji", "effects": "Efekty", "inventory_threshold": "Niski próg zapasu", "mobile_card_size": "Rozmiar karty mobilnej", "page": "Strona", "product_and_card_title_case": "Wielkość liter tytułu produktu i karty", "product_title_case": "Wielkość liter tytułu produktu", "reflection_opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odbicia", "right_padding": "Wypełnienie z prawej", "show_inventory_quantity": "Wyświetl niski poziom zapasu", "text_label_case": "Wielkość liter etykiety tekstowej", "transition_to_main_product": "Przejście z karty produktu do strony produktu", "show_second_image_on_hover": "Pokaż drugi obraz po najechaniu kursorem", "media": "Multimedia", "product_card_carousel": "Wyświ<PERSON><PERSON>ę", "media_fit": "Dopasowanie multimediów", "scroll_speed": "Czas do następnego ogłoszenia", "show_powered_by_shopify": "<PERSON><PERSON><PERSON> \"Powered by Shopify\"", "gift_card_form": "<PERSON>rz karty prezentowej"}, "options": {"adapt_to_image": "Dostosuj do obrazu", "apple": "Jabłko", "arrow": "Strzałka", "banana": "<PERSON><PERSON>", "bottle": "Butel<PERSON>", "box": "Skrzynka", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carrot": "Marchewka", "center": "Środek", "chat_bubble": "Dymek czatu", "clipboard": "Podkładka do pisania", "contain": "Zawiera", "counter": "Licznik", "cover": "Strona tytułowa", "custom": "Niestandardowy", "dairy_free": "Bezmleczne", "dairy": "<PERSON><PERSON><PERSON>", "dropdowns": "<PERSON><PERSON>", "dots": "<PERSON><PERSON><PERSON>", "dryer": "Suszarka", "end": "Koniec", "eye": "<PERSON><PERSON>", "facebook": "Facebook", "fire": "Ogień", "gluten_free": "<PERSON>z glutenu", "heart": "<PERSON><PERSON>", "horizontal": "W poziomie", "instagram": "Instagram", "iron": "Żelazko", "large": "<PERSON><PERSON><PERSON>", "leaf": "<PERSON><PERSON><PERSON>", "leather": "Skóra", "lightning_bolt": "Błyskawica", "lipstick": "Pomadka do ust", "lock": "Zamek", "map_pin": "Pinezka na mapie", "medium": "Średni", "none": "Brak", "numbers": "<PERSON><PERSON><PERSON>", "nut_free": "Bez orzechów", "pants": "<PERSON><PERSON><PERSON><PERSON>", "paw_print": "Odcisk łapy", "pepper": "Pie<PERSON>rz", "perfume": "Perfumy", "pinterest": "Pinterest", "plane": "<PERSON><PERSON><PERSON>", "plant": "<PERSON><PERSON><PERSON><PERSON>", "price_tag": "Metka z ceną", "question_mark": "Znak zapytania", "recycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON>", "ruler": "<PERSON><PERSON><PERSON><PERSON>", "serving_dish": "Naczynie do serwowania", "shirt": "<PERSON><PERSON><PERSON>", "shoe": "But", "silhouette": "Sylwetka", "small": "Ma<PERSON><PERSON>", "snapchat": "Snapchat", "snowflake": "Płatek <PERSON>gu", "star": "Gwiazdka", "start": "Początek", "stopwatch": "<PERSON>er", "tiktok": "TikTok", "truck": "Ciężarówka", "tumblr": "Tumblr", "twitter": "X (Twitter)", "vertical": "W pionie", "vimeo": "Vimeo", "washing": "<PERSON><PERSON><PERSON>", "auto": "Automatyczny", "default": "Domyślny", "fill": "Wypełnienie", "fit": "Dopasowanie", "full": "Pełne", "full_and_page": "Pełne tło, zawartość na szerokość strony", "heading": "Nagłówek", "landscape": "Poziomo", "lg": "LG", "link": "Link", "lowercase": "małe litery", "m": "M", "outline": "<PERSON><PERSON><PERSON><PERSON>", "page": "Strona", "portrait": "<PERSON><PERSON><PERSON>", "s": "S", "sentence": "<PERSON><PERSON><PERSON>", "solid": "Pełny", "space_between": "Odstęp między", "square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uppercase": "Duże litery", "circle": "<PERSON><PERSON><PERSON>", "swatches": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "full_and_page_offset_left": "Pełne tło, treść na szerokość strony, przesunięcie w lewo", "full_and_page_offset_right": "Pełne tło, treść na szerokość strony, przesunięcie w prawo", "offset_left": "Przesunięcie w lewo", "offset_right": "Przesunięcie w prawo", "page_center_aligned": "Strona z wyrównaniem do środka", "page_left_aligned": "Strona wyrównana do lewej", "page_right_aligned": "Strona wyrównana do prawej", "button": "Przycisk", "caption": "<PERSON><PERSON>", "h1": "Nagłówek 1", "h2": "Nagłówek 2", "h3": "Nagłówek 3", "h4": "Nagłówek 4", "h5": "Nagłówek 5", "h6": "Nagłówek 6", "paragraph": "Aka<PERSON>", "primary": "Główna", "secondary": "Dodatkowa", "tertiary": "Trzeciorzędna", "chevron_left": "Pagon skierowany w lewo", "chevron_right": "Pagon skierowany w prawo", "diamond": "Romb", "grid": "Siatka", "parallelogram": "Równoległobok", "rounded": "Zaokrąglenie", "fit_content": "Dopasowanie", "pills": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heavy": "<PERSON><PERSON><PERSON>", "thin": "<PERSON><PERSON><PERSON>", "drawer": "S<PERSON><PERSON>lad<PERSON>", "preview": "Podgląd", "text": "Tekst", "video_uploaded": "Przesłany", "video_external_url": "Zewnętrzny adres URL", "up": "W górę", "down": "<PERSON> dół", "gradient": "Gradient", "fixed": "Stałe", "pixel": "<PERSON><PERSON><PERSON>", "percent": "Procent", "aspect_ratio": "Współczynnik proporcji", "above_carousel": "<PERSON><PERSON><PERSON><PERSON>", "all": "Wszystkie", "always": "<PERSON><PERSON><PERSON>", "arrows_large": "Duże strzałki", "arrows": "Strz<PERSON>ł<PERSON>", "balance": "Balance", "bento": "<PERSON><PERSON>", "black": "<PERSON><PERSON><PERSON>", "bluesky": "<PERSON><PERSON>", "body_large": "Tekst podstawowy (duży)", "body_regular": "Tekst podstawowy (standardowy)", "body_small": "Tekst podstawowy (mały)", "bold": "Pogrubienie", "bottom_left": "Lewy dolny", "bottom_right": "<PERSON><PERSON><PERSON> dolny", "bottom": "<PERSON><PERSON><PERSON>", "capitalize": "Wielkie litery", "caret": "Trójkątna strzałka", "carousel": "<PERSON><PERSON><PERSON><PERSON>", "check_box": "Pole wyboru", "chevron_large": "Duża jodełka", "chevron": "Jodełka", "chevrons": "Jodełka", "classic": "Klasyczny", "collection_images": "<PERSON><PERSON><PERSON>", "color": "<PERSON><PERSON>", "complementary": "Uzupełniające", "dissolve": "Rozmyj", "dotted": "Kropkowane", "editorial": "Redakcyjny", "extra_large": "<PERSON><PERSON><PERSON>", "extra_small": "<PERSON><PERSON><PERSON>", "featured_collections": "Polecane kole<PERSON>cje", "featured_products": "Polecane produkty", "font_primary": "Główna", "font_secondary": "Dodatkowy", "font_tertiary": "Trzeciorzędna", "forward": "<PERSON><PERSON>", "full_screen": "<PERSON>b pełnoekranowy", "heading_extra_large": "Nagł<PERSON><PERSON> (bardzo duży)", "heading_extra_small": "Nagł<PERSON><PERSON> (bardzo mały)", "heading_large": "Nagłówek (duży)", "heading_regular": "Nagł<PERSON><PERSON> (standardowy)", "heading_small": "Nagł<PERSON><PERSON> (mały)", "icon": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "input": "<PERSON>", "inside_carousel": "<PERSON>wn<PERSON><PERSON><PERSON>", "inverse_large": "Odwró<PERSON><PERSON>", "inverse": "Odwrócone", "large_arrows": "Duże strzałki", "large_chevrons": "Duża jodełka", "left": "<PERSON><PERSON> strona", "light": "<PERSON><PERSON><PERSON>", "linkedin": "LinkedIn", "loose": "Luźny", "media_first": "Multimedia w pierwszej kolejności", "media_second": "Multimedia w drugiej kolejności", "modal": "<PERSON>b modalny", "narrow": "<PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON><PERSON>", "next_to_carousel": "Obok karuzeli", "normal": "Standard", "nowrap": "Bez zawijania", "off_media": "<PERSON><PERSON>", "on_media": "<PERSON> <PERSON><PERSON>", "on_scroll_up": "Przy przewijaniu w górę", "one_half": "1/2", "one_number": "1", "one_third": "1/3", "pill": "Okrągły przełącznik", "plus": "Plus", "pretty": "Pretty", "price": "<PERSON><PERSON>", "primary_style": "Styl podstawowy", "rectangle": "Prostokąt", "regular": "Zwykły", "related": "Powiązane", "reverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rich_text": "Tekst sformatowany", "right": "<PERSON><PERSON><PERSON> strona", "secondary_style": "<PERSON><PERSON>", "semibold": "Półpogrubiony", "shaded": "C<PERSON>iowan<PERSON>", "show_second_image": "Pokaż drugi obraz", "single": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slide_left": "Przesuń w lewo", "slide_up": "Przesuń w górę", "spotify": "Spotify", "stack": "Stos", "text_only": "Tylko tekst", "threads": "Threads", "thumbnails": "Miniatury", "tight": "Tight", "top_left": "U góry po lewej", "top_right": "U góry po prawej", "top": "Góra", "two_number": "2", "two_thirds": "2/3", "underline": "Podkreślenie", "video": "Film", "wide": "Szeroki", "youtube": "Youtube", "accent": "<PERSON><PERSON><PERSON>", "below_image": "Pod obrazem", "body": "Tekst podstawowy", "button_primary": "Przycisk główny", "button_secondary": "Przycisk dodatkowy", "compact": "Kompaktowa", "crop_to_fit": "P<PERSON><PERSON><PERSON>j, aby dopasować", "hidden": "Ukryte", "hint": "Wskazówka", "maintain_aspect_ratio": "Zachowaj współczynnik proporcji", "off": "W<PERSON>ł.", "on_image": "<PERSON> obrazie", "social_bluesky": "Media społecznościowe: Bluesky", "social_facebook": "Media społecznościowe: Facebook", "social_instagram": "Media społecznościowe: Instagram", "social_linkedin": "Media społecznościowe: LinkedIn", "social_pinterest": "Media społecznościowe: Pinterest", "social_snapchat": "Media społecznościowe: Snapchat", "social_spotify": "Media społecznościowe: Spotify", "social_threads": "Media społecznościowe: Threads", "social_tiktok": "Media społecznościowe: TikTok", "social_tumblr": "Media społecznościowe: Tumblr", "social_twitter": "Media społecznościowe: X (Twitter)", "social_whatsapp": "Media społecznościowe: WhatsApp", "social_vimeo": "Media społecznościowe: Vimeo", "social_youtube": "Media społecznościowe: YouTube", "spotlight": "Spotlight", "standard": "Standardowa", "subheading": "Nagłówek podrzędny", "blur": "<PERSON><PERSON><PERSON>", "lift": "Uniesienie", "reveal": "Po<PERSON><PERSON>", "scale": "Skalowanie", "subtle_zoom": "Powiększenie"}, "content": {"background_video": "Film w tle", "describe_the_video_for": "Opisz film dla klientów korzystających z czytników ekranu. [Dow<PERSON>z się więcej](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "width_is_automatically_optimized": "S<PERSON>okość jest automatycznie dostosowywana do urządzeń mobilnych.", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "background_image": "<PERSON><PERSON>z tła", "block_size": "Roz<PERSON>r bloku", "borders": "Obramowania", "section_size": "<PERSON><PERSON><PERSON><PERSON>", "slideshow_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> slajdu", "typography": "Typografia", "complementary_products": "Produkty uzupełniające należy skonfigurować za pomocą aplikacji Search & Discovery. [Dow<PERSON>z się więcej](https://help.shopify.com/manual/online-store/search-and-discovery)", "mobile_column_optimization": "Kolumny będą automatycznie optymalizowane pod kątem urządzeń mobilnych", "content_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> treści ma zastosowanie tylko wtedy, g<PERSON> szer<PERSON><PERSON> sekcji jest ustawiona na pełny zakres.", "adjustments_affect_all_content": "Dotyczy całej zawartości w tym bloku", "responsive_font_sizes": "Rozmiary są automatycznie skalowane dla wszystkich rozmiarów ekranów", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swatches": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variant_settings": "Ustawienia wariantów", "background": "Tło", "cards_layout": "Układ kart", "section_layout": "<PERSON><PERSON><PERSON><PERSON>", "mobile_size": "Rozmiar mobilny", "appearance": "Wygląd", "arrows": "Strz<PERSON>ł<PERSON>", "body_size": "Rozmiar tekstu podstawowego", "bottom_row_appearance": "Wygląd dolnego rzędu", "carousel_navigation": "<PERSON><PERSON><PERSON><PERSON> po ka<PERSON>i", "carousel_pagination": "<PERSON><PERSON><PERSON><PERSON>", "copyright": "Prawo autorskie", "edit_logo_in_theme_settings": "Edytuj logo w [usta<PERSON><PERSON><PERSON> szab<PERSON>](/editor?context=theme&category=logo%20and%20favicon)", "edit_price_in_theme_settings": "Edytuj format cen w [usta<PERSON><PERSON><PERSON> sza<PERSON>lonu](/editor?context=theme&category=currency%20code)", "edit_variants_in_theme_settings": "Edytuj styl wariantów w [ustawieniach szablonu](/editor?context=theme&category=variants)", "email_signups_create_customer_profiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> [profile klienta](https://help.shopify.com/manual/customers)", "follow_on_shop_eligiblity": "Aby przy<PERSON>k był w<PERSON>, kanał Shop musi być zainstalowany, a usługa Shop Pay musi być aktywna. [<PERSON><PERSON><PERSON> się więcej](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)", "fonts": "Czcionki", "grid": "Siatka", "heading_size": "Rozmiar nagłówka", "image": "<PERSON><PERSON><PERSON>", "input": "<PERSON>", "layout": "<PERSON><PERSON><PERSON><PERSON>", "link": "Link", "link_padding": "Wypełnienie linku", "localization": "Lokalizacja", "logo": "Logo", "margin": "Marża", "media": "Multimedia", "media_1": "Multimedia 1", "media_2": "Multimedia 2", "menu": "<PERSON><PERSON>", "mobile_layout": "Układ na urządzeniu mobilnym", "padding": "Wypełnienie", "padding_desktop": "Wypełnienie pulpitu", "paragraph": "Aka<PERSON>", "policies": "Zasady", "popup": "Wyskakują<PERSON> okienko", "search": "Wyszukiwanie", "size": "Rozmiar", "social_media": "Media społecznościowe", "submit_button": "Przycisk Prześlij", "text_presets": "Ustawienia wstępne tekstu", "transparent_background": "Przezroczyste tło", "typography_primary": "Typografia podstawowa", "typography_secondary": "Typografia dodatkowa", "typography_tertiary": "Typografia trzeciorzędna", "mobile_width": "Szerokość na urządzeniu mobilnym", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON><PERSON>", "colors": "<PERSON><PERSON><PERSON>", "collection_page": "Strona kolekcji", "copyright_info": "<PERSON><PERSON><PERSON>, jak [ed<PERSON><PERSON><PERSON>je oświadczenie o prawach autorskich](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)", "customer_account": "<PERSON><PERSON> k<PERSON>", "edit_empty_state_collection_in_theme_settings": "Edytuj kolekcję pustych stanów w [ustawieniach szablonu](/editor?context=theme&category=search)", "home_page": "Strona główna", "images": "Zdjęcia", "inverse_logo_info": "Używana po ustawieniu transparentnego tła nagłówka na „Odwrócenie”", "manage_customer_accounts": "[Zarz<PERSON>d<PERSON><PERSON> w<PERSON>](/admin/settings/customer_accounts) w ustawieniach konta klienta. Starsze konta nie są obsługiwane.", "manage_policies": "[Zarządzaj politykami](/admin/settings/legal)", "product_page": "Strona produktu", "text": "Tekst", "thumbnails": "Miniatury", "visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visible_if_collection_has_more_products": "<PERSON><PERSON><PERSON><PERSON>, jeśli kolekcja ma więcej produktów niż pokazano", "grid_layout": "<PERSON><PERSON><PERSON><PERSON>", "app_required_for_ratings": "Do oceniania produktów wymagana jest aplikacja. [<PERSON><PERSON><PERSON> się więcej](https://help.shopify.com/manual/apps)", "icon": "<PERSON><PERSON><PERSON>", "manage_store_name": "[Zarząd<PERSON><PERSON> nazwą sklepu](/admin/settings/general?edit=storeName)", "resource_reference_collection_card": "Wyświetla kolekcję z sekcji nadrzędnej", "resource_reference_collection_card_image": "Wyświetla obraz z kolekcji nadrzędnej", "resource_reference_collection_title": "Wyświetla tytuł z kolekcji nadrzędnej", "resource_reference_product": "Automatycznie łączy z produktem nadrzędnym", "resource_reference_product_card": "Wyświetla produkt z sekcji nadrzędnej", "resource_reference_product_inventory": "Wyświetla zapas z produktu nadrzędnego", "resource_reference_product_price": "Wyświetla cenę z produktu nadrzędnego", "resource_reference_product_recommendations": "Wyświetla polecenia na podstawie produktu nadrzędnego", "resource_reference_product_review": "Wyświetla recenzje z produktu nadrzędnego", "resource_reference_product_swatches": "Wyświetla próbki z produktu nadrzędnego", "resource_reference_product_title": "Wyświetla tytuł z produktu nadrzędnego", "resource_reference_product_variant_picker": "Wyświetla warianty z produktu nadrzędnego", "resource_reference_product_media": "Wyświetla multimedia z produktu nadrzędnego", "product_media": "Multimedia produktu", "section_link": "<PERSON> sek<PERSON>", "gift_card_form_description": "Klienci mogą wysyłać karty prezentowe na adres e-mail odbiorcy wraz z osobistą wiadomością. [Dow<PERSON><PERSON> się więcej](https://help.shopify.com/manual/products/gift-card-products)"}, "html_defaults": {"share_information_about_your": "<p>Udostępnij klientom informacje o swojej marce. Opisz produkt, udostępnij ogłoszenia lub przywitaj klientów w swoim sklepie.</p>"}, "text_defaults": {"collapsible_row": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "button_label": "<PERSON><PERSON>", "heading": "Nagłówek", "email_signup_button_label": "Subskrybuj", "accordion_heading": "Nagłówek akordeonu", "contact_form_button_label": "Prześ<PERSON>j", "popup_link": "Wyskakujący link", "sign_up": "Zarejestruj się", "welcome_to_our_store": "Witamy w naszym sklepie", "be_bold": "Odważ się.", "shop_our_latest_arrivals": "Odkryj nasze najnowsze produkty!"}, "info": {"video_alt_text": "Opisz film dla użytkowników technologii asystujących", "video_autoplay": "Filmy będą domyślnie wyciszone", "video_external": "Użyj adresu URL do YouTube lub Vimeo", "link_info": "Opcjonalnie: umożliwia kliknięcie ikony", "carousel_layout_on_mobile": "Ka<PERSON>zela jest używana na urządzeniach mobilnych", "carousel_hover_behavior_not_supported": "Najechanie kursorem na karuzelę nie jest obsługiwane, gdy wybrano karuzelę na poziomie sekcji.", "checkout_buttons": "Umożliwia kupującym szybsze dokonanie zakupu i może poprawić konwersję. [<PERSON><PERSON><PERSON> się więcej](https://help.shopify.com/manual/online-store/dynamic-checkout)", "custom_heading": "Niestandardowy nagłówek", "edit_presets_in_theme_settings": "Edytuj ustawienia wstępne w [ustawi<PERSON>ch szablonu](/editor?context=theme&category=typography)", "enable_filtering_info": "Dostosuj filtry za pomocą [aplikacji Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)", "grid_layout_on_mobile": "Uk<PERSON><PERSON> siatki jest używany dla urządzeń mobilnych", "logo_font": "<PERSON> zastosowanie tylko wtedy, gdy <PERSON> nie jest wybrane", "manage_countries_regions": "[Zarządzaj krajami/regionami](/admin/settings/markets)", "manage_languages": "[Zarząd<PERSON><PERSON> j<PERSON>](/admin/settings/languages)", "transparent_background": "Sprawdź ka<PERSON><PERSON>, w którym zastosowano przezroczyste tło, pod kątem czytelności", "aspect_ratio_adjusted": "Dostosowano w niektórych układach", "auto_open_cart_drawer": "Gdy ta opcja jest aktywna, szu<PERSON>lada koszyka otworzy się automatycznie po dodaniu produktu do koszyka.", "custom_liquid": "Dodaj fragmenty kodu aplikacji lub inny kod, aby ut<PERSON><PERSON><PERSON><PERSON> zaawansowane dostosowania. [<PERSON><PERSON><PERSON> się więcej](https://shopify.dev/docs/api/liquid)", "applies_on_image_only": "Dotyczy tylko obrazów", "hover_effects": "Dotyczy kart produktów i kolekcji", "pills_usage": "Używane przez zastosowane filtry, kody rabatowe i sugestie wyszukiwania"}, "categories": {"product_list": "<PERSON><PERSON><PERSON>", "basic": "Basic", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_list": "Lista kolekcji", "footer": "Stopka", "forms": "Formularze", "header": "Nagłówek", "layout": "<PERSON><PERSON><PERSON><PERSON>", "links": "<PERSON><PERSON>", "product": "Produkt", "banners": "Banery", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "Niestandardowy", "decorative": "<PERSON><PERSON><PERSON><PERSON>", "products": "Produkty", "other_sections": "Inny", "storytelling": "Opowiadanie historii"}}