{%- doc -%}
  Renders a swatches variant picker, used within the product-swatches block.

  @param {object} product_resource - The product object, which contains variants and options.
  @param {boolean} [has_option_selected] - Whether an option is already selected.

  @example
  {% render 'variant-swatches', product_resource: product %}
{%- enddoc -%}

<swatches-variant-picker-component
  data-product-id="{{ product_resource.id }}"
  data-product-url="{{ product_resource.url }}"
  ref="swatchesVariantPicker"
>
  <form>
    {%- for product_option in product_resource.options_with_values -%}
      {%- liquid
        assign swatch_count = product_option.values | map: 'swatch' | compact | size
      -%}

      {% if swatch_count == 0 %}
        {% continue %}
      {% endif %}

      {%- liquid
        assign product_has_combined_listing = closest.product.options_with_values | map: 'values' | map: 'product_url' | compact | size

        # Only apply our custom logic if nothing is selected (initial page load)
        if has_option_selected != true and product_has_combined_listing == 0
          # Logic to determine which swatch should be pre-selected
          assign first_image = product_resource.media.first
          assign variant_images = product_resource.images | where: 'attached_to_variant?', true
          assign swatch_to_preselect = null
          if swatch_count == 1
            # Single swatch: Always pre-select it
            assign swatch_to_preselect = product_option.values.first
          elsif swatch_count > 1
            if first_image and variant_images contains first_image
              # First image is a variant image - find which variant it belongs to
              for option_value in product_option.values
                if option_value.variant.featured_media.id == first_image.id
                  assign swatch_to_preselect = option_value
                  break
                endif
              endfor
            elsif variant_images.size == 0
              # No variants have images - pre-select first swatch
              assign swatch_to_preselect = product_option.values.first
            else
              assign none_checked = true
            endif
            # else: First image is NOT a variant image - don't pre-select any swatch
          endif
        endif

        # Identify which option position this swatch option is
        # Use product_option.position which is the actual position among ALL options
        assign swatch_option_position = product_option.position
        assign swatch_option_key = 'option' | append: swatch_option_position
      -%}

      <fieldset class="variant-option variant-option--buttons variant-option--swatches">
        {% capture children %}
          {%- for product_option_value in product_option.values -%}
            {% liquid
              assign featured_media = product_option_value.variant.featured_media

              # If the variant has no featured media, and we have a combined listing product, then fall back to using the
              # featured media of the child product that is linked to this option value.
              if featured_media == blank and product_option_value.product_url
                assign featured_media = product_option_value.variant.product.featured_media
              endif

              if has_option_selected != true and product_has_combined_listing == 0
                # Determine if this swatch should be checked
                assign is_checked = false
                if swatch_to_preselect != nil and swatch_to_preselect.id == product_option_value.id
                  assign is_checked = true
                  assign none_checked = false
                endif
              endif

              # Use Liquid filters to check if any variant with this swatch option value is available
              # swatch_option_key was set in the parent loop (e.g., 'option1', 'option2', etc.)
              assign available_variants = product_resource.variants | where: swatch_option_key, product_option_value | where: 'available', true
              assign first_available_variant = available_variants | first
              assign available_count = available_variants | size
            %}

            <li class="variant-option__swatch">
              <label
                data-media-id="{{ featured_media.id }}"
                class="variant-option__button-label variant-option__button-label--has-swatch swatch-rounded"
                on:pointerenter="product-card/previewVariant/{{ featured_media.id }}"
                on:pointerleave="product-card/resetVariant"
              >
                <input
                  type="radio"
                  name="{{ product_option.name | escape }}-{{ product_resource.id }}-swatch"
                  value="{{ product_option_value | escape }}"
                  aria-label="{{ product_option_value.name }}"
                  data-input-id="{{ product_option.position }}-{{ forloop.index0 }}"
                  data-option-media-id="{{ featured_media.id }}"
                  data-option-value-id="{{ product_option_value.id }}"
                  data-option-available="{{ product_option_value.available }}"
                  data-connected-product-url="{{ product_option_value.product_url }}"
                  {% if product_option_value.variant.id %}
                    data-variant-id="{{ product_option_value.variant.id }}"
                  {% endif %}
                  {% if none_checked != true %}
                    {% if product_option_value.selected or is_checked == true %}
                      checked
                    {% endif %}
                  {% endif %}
                  data-available-count="{{ available_count }}"
                  data-first-available-or-first-variant-id="{{ first_available_variant.id | default: product_option_value.variant.id }}"
                >
                {% render 'swatch',
                  swatch: product_option_value.swatch,
                  variant_image: featured_media,
                %}
                {% render 'strikethrough-variant', product_option: product_option_value %}
              </label>
            </li>
          {%- endfor -%}
          <li
            slot="more"
          >
            <button
              class="hidden-swatches__count"
              aria-label="{{ 'actions.show_all_options' | t }}"
              on:click="/showAllSwatches"
            ></button>
          </li>
        {% endcapture %}

        {% render 'overflow-list', children: children, ref: 'overflowList', defer: true %}
      </fieldset>
    {%- endfor -%}
    <script type="application/json">
      {{ product_resource.selected_or_first_available_variant | json }}
    </script>
  </form>
</swatches-variant-picker-component>
