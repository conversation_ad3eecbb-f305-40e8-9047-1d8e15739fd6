
{% liquid
  assign unset_image_tag = false
  if block.settings.image_position == 'contain'
    assign unset_image_tag = true
  endif
%}

{% render 'media', unset_image_tag: unset_image_tag, section_id: section.id %}

{% schema %}
{
  "name": "t:names.media",
  "tag": null,
  "settings": [
    {
      "type": "select",
      "id": "media_type",
      "label": "t:settings.type",
      "options": [
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:settings.image",
      "visible_if": "{{ block.settings.media_type == 'image' }}"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link",
      "visible_if": "{{ block.settings.media_type == 'image'  }}"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:settings.video",
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "video_loop",
      "label": "t:settings.loop",
      "default": true,
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "video_autoplay",
      "label": "t:settings.autoplay",
      "default": false,
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:settings.image_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "contain ",
          "label": "t:options.contain"
        }
      ],
      "default": "cover",
      "visible_if": "{{ block.settings.media_type == 'image' }}"
    },
    {
      "type": "select",
      "id": "video_position",
      "label": "t:settings.video_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "contain",
          "label": "t:options.contain"
        }
      ],
      "default": "cover",
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    }
  ],
  "presets": [
    {
      "name": "t:names.media"
    }
  ]
}
{% endschema %}
