{%- doc -%}
  Intended for use in a block similar to the button block.

  @param {string} link - link to render
  @param {object} [block] - The block

  @example
  {% render 'button', link: '/collections/all' %}
{%- enddoc -%}

{% assign block_settings = block.settings %}

<a
  {% if link == blank %}
    role="link"
    aria-disabled="true"
  {% else %}
    href="{{ link }}"
  {% endif %}
  class="
    size-style
    {{ block_settings.style_class }}
    {{ block_settings.style_class }}--{{ block.id }}
  "
  style="{% render 'size-style', settings: block_settings %}"
  {%- if block_settings.open_in_new_tab -%}
    target="_blank"
    rel="noopener noreferrer"
  {%- endif -%}
  {{ block.shopify_attributes }}
>
  {{ block_settings.label }}
</a>

{% stylesheet %}
  .link {
    text-decoration: none;
    text-decoration-color: currentcolor;

    &:hover {
      color: var(--color-primary-hover);
      text-decoration-color: transparent;
    }
  }
{% endstylesheet %}
