{%- doc -%}
  Renders a slideshow arrow control

  @param {string} action - { 'previous' | 'next' } The action to perform when the arrow is clicked.
  @param {string} [icon_style] - { 'arrow' | 'chevron' } The style of the icon, defaults to 'arrow'.
  @param {string} [icon_shape] - { 'none' | 'circle' | 'square' } The shape of the icon background, defaults to 'none'.
  @param {string} [icon_size] - { 'small' | 'medium' | 'large' } The size of the icon, defaults to 'medium'.

  @example
  {%- render 'slideshow-arrow', action: 'previous' -%}
{%- enddoc -%}

{%- liquid
  assign icon_name = 'arrow'
  assign style = icon_style | default: 'arrow'
  assign shape = icon_shape | default: 'none'

  if icon_style contains 'chevron'
    assign icon_name = 'caret'
  endif

  if icon_style contains 'large'
    assign icon_size = 'large'
  endif
-%}

<button
  class="
    slideshow-control slideshow-control--{{ action }}
    {% if icon_size %} slideshow-control--{{ icon_size }}{% endif %}
    {% if shape != 'none' %} slideshow-control--shape-{{ shape }}{% endif %}
    slideshow-control--style-{{ style }}
    button button-unstyled button-unstyled--transparent
    {% if action == 'previous' %} flip-x{% endif %}
  "
  {% # theme-check-disable %}
  aria-label="{{ 'accessibility.slideshow_' | append: action | t }}"
  {% # theme-check-enable %}
  on:click="/{{ action }}"
  ref="{{ action }}"
>
  <span class="svg-wrapper icon-{{ icon_name }}">
    {%- if icon_name == 'caret' -%}
      {{- 'icon-caret.svg' | inline_asset_content -}}
    {%- else -%}
      {{- 'icon-arrow.svg' | inline_asset_content -}}
    {%- endif -%}
  </span>
</button>
