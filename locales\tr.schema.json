{"names": {"404": "404", "borders": "Kenarlıklar", "collapsible_row": "Daraltılabilir <PERSON>", "custom_section": "<PERSON><PERSON>", "icon": "<PERSON>m<PERSON>", "logo_and_favicon": "Logo ve favicon", "product_buy_buttons": "Satın al düğmeleri", "product_description": "<PERSON><PERSON>ı<PERSON><PERSON>", "product_price": "<PERSON><PERSON><PERSON>", "slideshow": "<PERSON><PERSON><PERSON> gö<PERSON>", "typography": "Baskı", "video": "Video", "colors": "Ren<PERSON>r", "overlapping_blocks": "Çakışan bloklar", "product_variant_picker": "Varyasyon <PERSON>ç<PERSON>", "slideshow_controls": "Slayt gösterisi denetimleri", "size": "<PERSON><PERSON>", "spacing": "Boşluk", "product_recommendations": "Önerilen ürünler", "product_media": "<PERSON><PERSON><PERSON><PERSON> medyası", "featured_collection": "Öne çıkan koleksiyon", "add_to_cart": "Sepete ekle", "email_signup": "E-posta kaydı", "submit_button": "<PERSON><PERSON><PERSON>", "grid_layout_selector": "Izgara düzeni seçicisi", "image": "G<PERSON><PERSON><PERSON>", "list_items": "Liste öğeleri", "facets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_cards": "<PERSON>rün kartları", "styles": "Stiller", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": "<PERSON><PERSON><PERSON><PERSON>", "primary_button": "<PERSON><PERSON><PERSON><PERSON>", "secondary_button": "<PERSON><PERSON><PERSON><PERSON>", "popovers": "Popover", "pull_quote": "Alıntı", "contact_form": "İletişim formu", "featured_product": "<PERSON><PERSON><PERSON><PERSON>", "icons_with_text": "<PERSON><PERSON> i<PERSON> sim<PERSON>", "marquee": "<PERSON><PERSON> yazı", "products_carousel": "Öne çıkan koleksiyon: Carousel", "products_grid": "Öne çıkan koleksiyon: Izgara", "alternating_content_rows": "<PERSON><PERSON><PERSON><PERSON>", "product_list": "Öne çıkan koleksiyon", "spacer": "Aralayıcı", "accelerated_checkout": "Hızlı ödeme", "accordion": "Akordeon", "accordion_row": "Akordeon satırı", "animations": "Animasyonlar", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "<PERSON><PERSON><PERSON>", "badges": "<PERSON><PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>", "cart": "Sepet", "cart_items": "Sepet ürünleri", "cart_products": "Sepet ürünleri", "cart_title": "Sepet", "collection": "Koleksiyon", "collection_card": "Koleksiyon kartı", "collection_columns": "Koleksiyon sütunları", "collection_container": "Koleksiyon", "collection_description": "Koleksiyon açıklaması", "collection_image": "Koleksiyon görseli", "collection_info": "Koleksiyon bilgileri", "collection_list": "Koleks<PERSON><PERSON>esi", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collections_bento": "Koleksiyon listesi: <PERSON><PERSON>", "collections_carousel": "Koleksiyon listesi: Carousel", "collections_grid": "Koleksiyon listesi: Izgara", "content": "İçerik", "content_grid": "İçerik ızgarası", "details": "Ayrıntılar", "divider": "Ayırıcı", "divider_section": "Ayırıcı", "faq_section": "SSS", "filters": "Filtreleme ve sıralama", "follow_on_shop": "Shop'ta takip edin", "footer": "Altbilgi", "footer_utilities": "Altbilgi yardımcı araçları", "group": "Grup", "header": "Üstbilgi", "heading": "Başlık", "hero": "Hero", "icons": "<PERSON><PERSON><PERSON><PERSON>", "image_with_text": "<PERSON><PERSON>", "input": "<PERSON><PERSON><PERSON>", "logo": "Logo", "magazine_grid": "Dergi ı<PERSON>ı", "media": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "mobile_layout": "<PERSON><PERSON>", "payment_icons": "<PERSON><PERSON><PERSON> si<PERSON>", "popup_link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere bağlantısı", "predictive_search": "<PERSON><PERSON><PERSON> pencer<PERSON>", "predictive_search_empty": "<PERSON><PERSON><PERSON> bo<PERSON>", "price": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "product_card": "Ürün kartı", "product_card_media": "<PERSON><PERSON><PERSON>", "product_card_rendering": "Ürün kartı işleme", "product_grid": "Izgara", "product_grid_main": "Ürün ızgarası", "product_image": "<PERSON><PERSON><PERSON><PERSON>", "product_information": "<PERSON><PERSON><PERSON>n bilgi<PERSON>i", "product_review_stars": "Değerlendirme yıldızları", "quantity": "<PERSON><PERSON>", "row": "Satır", "search": "Ara", "section": "Bölüm", "selected_variants": "Seçilen <PERSON>", "shop_the_look": "Bu tarz i<PERSON>in al<PERSON> başlayın", "slide": "<PERSON><PERSON><PERSON>", "social_media_links": "Sosyal medya ba<PERSON>ı<PERSON>ı", "steps": "<PERSON><PERSON><PERSON>lar", "summary": "Özet", "swatches": "<PERSON><PERSON><PERSON>", "testimonials": "Kullanıcı Görüşleri", "text": "<PERSON><PERSON>", "title": "Başlık", "utilities": "Yardımcı araçlar", "video_section": "Video", "jumbo_text": "Jumbo metin", "collection_title": "Koleksiyon başlığı", "view_all_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "search_input": "<PERSON><PERSON><PERSON>", "search_results": "<PERSON><PERSON>", "read_only": "Salt okunur", "collection_links": "Koleksiyon bağlantıları", "count": "<PERSON><PERSON>", "custom_liquid": "Özel liquid", "blog": "Blog", "blog_post": "Blog gönderisi", "blog_posts": "Blog gönderileri", "caption": "Alt yazı", "collection_card_image": "G<PERSON><PERSON><PERSON>", "collection_links_spotlight": "Koleksiyon bağlantıları: Spotlight", "collection_links_text": "Koleksiyon bağlantıları: <PERSON>in", "collections_editorial": "Koleksiyon listesi: Editor<PERSON>", "copyright": "Telif <PERSON>", "drawers": "Çekmeceler", "editorial": "Başyazı", "editorial_jumbo_text": "Başyazı: <PERSON><PERSON><PERSON><PERSON>k metin", "hero_marquee": "Hero: <PERSON><PERSON>", "input_fields": "<PERSON><PERSON><PERSON>", "local_pickup": "Mağazadan teslim alım", "marquee_section": "<PERSON><PERSON> yazı", "media_with_text": "<PERSON><PERSON> i<PERSON> medya", "page": "Say<PERSON>", "page_content": "İçerik", "page_layout": "<PERSON><PERSON>", "policy_list": "Politika bağlantıları", "prices": "<PERSON><PERSON><PERSON><PERSON>", "products_editorial": "Öne çıkan koleksiyon: Editoryal", "social_link": "Sosyal medya bağlantısı", "split_showcase": "Split showcase", "variant_pickers": "<PERSON><PERSON><PERSON><PERSON>", "product_title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı", "large_logo": "Büyük logo", "product_list_button": "<PERSON>ümü<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product_inventory": "<PERSON><PERSON><PERSON><PERSON>", "pills": "Seçenekler", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "featured_image": "<PERSON>ne <PERSON>"}, "settings": {"autoplay": "Otomatik oynatma", "background": "Arka plan", "border_radius": "Köşe yarıçapı", "border_width": "Kenarlık kalınlığı", "borders": "Kenarlıklar", "bottom_padding": "Alt dolgu", "color": "Renk", "content_direction": "İçerik yönü", "content_position": "İçerik konumu", "cover_image_size": "<PERSON><PERSON><PERSON> gö<PERSON><PERSON> boy<PERSON>u", "cover_image": "Kapak gö<PERSON>", "custom_width": "<PERSON><PERSON> g<PERSON>", "enable_video_looping": "Video döngüsü", "favicon": "Favicon", "heading": "Başlık", "icon": "<PERSON>m<PERSON>", "image_icon": "<PERSON><PERSON><PERSON><PERSON> simgesi", "make_section_full_width": "Bölümü tam genişlikli yap", "overlay_opacity": "<PERSON><PERSON> <PERSON>şımı opaklığı", "padding": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON>", "top_padding": "<PERSON>st dolgu", "video": "Video", "video_alt_text": "<PERSON><PERSON><PERSON><PERSON> metin", "video_loop": "Video döngüsü", "video_position": "Video konumu", "width": "Genişlik", "alignment": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>", "colors": "Ren<PERSON>r", "content_alignment": "İçerik hizalaması", "custom_minimum_height": "Özel minimum yükseklik", "font_family": "Yazı tipi ailesi", "gap": "Aralık", "geometric_translate_y": "<PERSON><PERSON>met<PERSON>", "image": "G<PERSON><PERSON><PERSON>", "image_opacity": "Görsel opaklığı", "image_position": "<PERSON><PERSON><PERSON><PERSON> konumu", "image_ratio": "Görsel oranı", "label": "Etiket", "line_height": "Sa<PERSON>ır yü<PERSON>ği", "link": "Bağlantı", "layout_gap": "Düzen aralığı", "minimum_height": "Minimum yükseklik", "opacity": "Opaklık", "primary_color": "Bağlantılar", "section_width": "<PERSON><PERSON><PERSON><PERSON><PERSON> genişliği", "size": "<PERSON><PERSON>", "slide_spacing": "Slayt aralığı", "slide_width": "<PERSON><PERSON><PERSON> genişliği", "slideshow_fullwidth": "Tam geni<PERSON>te slaytlar", "style": "Stil", "text_case": "Büyük/küçük harf durumu", "z_index": "Z endeksi", "limit_content_width": "İçerik genişliğini sınırla", "color_scheme": "Renk şeması", "inherit_color_scheme": "Renk şemasını devral", "product_count": "<PERSON><PERSON><PERSON><PERSON>", "product_type": "<PERSON><PERSON><PERSON><PERSON> türü", "content_width": "İçerik genişliği", "collection": "Koleksiyon", "enable_sticky_content": "Masaüstünde sabit içeriği etkinleştir", "error_color": "<PERSON><PERSON>", "success_color": "Başarılı", "primary_font": "Birincil yazı tipi", "secondary_font": "İkincil yazı tipi", "tertiary_font": "Üçüncül yazı tipi", "columns": "<PERSON><PERSON><PERSON><PERSON>", "items_to_show": "Gösterilecek öğeler", "layout": "D<PERSON>zen", "layout_type": "<PERSON><PERSON><PERSON>", "show_grid_layout_selector": "Izgara düzeni seçicisini göster", "view_more_show": "Daha fazlasını görüntüle düğmesini göster", "image_gap": "Görsel aralığı", "width_desktop": "Masaüstü genişliği", "width_mobile": "Mobil ekran genişliği", "border_style": "Kenarlık stili", "height": "Yükseklik", "thickness": "Kalınlık", "stroke": "<PERSON><PERSON><PERSON><PERSON>", "filter_style": "Filtre stili", "swatches": "<PERSON><PERSON><PERSON>", "quick_add_colors": "Hızlı renk ekleme", "divider_color": "Ayırıcı", "border_opacity": "<PERSON><PERSON><PERSON><PERSON>r <PERSON>ı<PERSON>", "hover_background": "Arka planı vurgula", "hover_borders": "Sınırı vurgula", "hover_text": "<PERSON><PERSON>", "primary_hover_color": "Bağlantıyı vurgula", "primary_button_text": "<PERSON><PERSON><PERSON><PERSON>", "primary_button_background": "Birincil düğme arka planı", "primary_button_border": "Birinc<PERSON> düğ<PERSON> sınırı", "secondary_button_text": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "secondary_button_background": "İkincil düğme arka planı", "secondary_button_border": "İkincil düğme sınırı", "shadow_color": "<PERSON><PERSON><PERSON>", "video_autoplay": "Otomatik oynatma", "video_cover_image": "Kapak gö<PERSON>", "video_external_url": "URL", "video_source": "<PERSON><PERSON><PERSON>", "card_image_height": "Ürün görseli yüksekliği", "background_color": "Arka plan rengi", "first_row_media_position": "<PERSON><PERSON><PERSON><PERSON> satır medya konumu", "hide_padding": "<PERSON><PERSON><PERSON><PERSON> gizle", "size_mobile": "<PERSON><PERSON>", "pixel_size_mobile": "<PERSON><PERSON><PERSON>", "percent_size_mobile": "<PERSON><PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "custom_mobile_size": "<PERSON>zel mobil boyutu", "fixed_height": "Piksel yüksekliği", "fixed_width": "<PERSON><PERSON><PERSON> genişliği", "percent_height": "Yüzde yüksekliği", "percent_width": "Yüzde genişliği", "percent_size": "<PERSON><PERSON><PERSON><PERSON>", "pixel_size": "<PERSON><PERSON><PERSON>", "logo_font": "Logo yazı tipi", "accordion": "Akordeon", "aspect_ratio": "En-boy oranı", "auto_rotate_announcements": "Duyuruları otomatik olarak döndür", "auto_rotate_slides": "Slaytları otomatik olarak döndür", "badge_corner_radius": "Köşe yarıçapı", "badge_position": "<PERSON><PERSON><PERSON><PERSON> konum", "badge_sale_color_scheme": "Satış", "badge_sold_out_color_scheme": "Tükendi", "behavior": "Davranış", "blur": "Bulanık gölge", "border": "Kenarlık", "bottom": "Alt", "carousel_on_mobile": "<PERSON><PERSON> c<PERSON> carousel", "cart_count": "Sepet sayısı", "cart_items": "Sepet ürünleri", "cart_related_products": "Alakalı ürünler", "cart_title": "Sepet", "cart_total": "Sepet toplamı", "cart_type": "<PERSON><PERSON><PERSON>", "case": "Büyük/küçük harf durumu", "checkout_buttons": "Hızlı ödeme düğmeleri", "collection_list": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_templates": "Koleksiyon şablonları", "content": "İçerik", "corner_radius": "Köşe yarıçapı", "country_region": "Ülke/Bölge", "currency_code": "Para birimi kodu", "custom_height": "Özel yükseklik", "desktop_height": "Masaüstü yüksekliği", "direction": "<PERSON><PERSON><PERSON>", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divider_thickness": "Ayırıcı kalınlığı", "divider": "Ayırıcı", "dividers": "<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "drop_shadow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empty_state_collection_info": "<PERSON><PERSON> g<PERSON>n önce gösterilir", "empty_state_collection": "Boş durum koleksiyonu", "enable_filtering": "<PERSON><PERSON><PERSON><PERSON>", "enable_grid_density": "Izgara düzeni kontrolü", "enable_sorting": "Sıralama", "enable_zoom": "Yakınlaştırmayı etkinleştir", "equal_columns": "<PERSON><PERSON><PERSON>", "expand_first_group": "Birinci grubu g<PERSON>", "extend_media_to_screen_edge": "Medyayı ekran kenarına genişlet", "extend_summary": "Ekran kenarına genişlet", "extra_large": "Çok büyük", "extra_small": "Çok küçük", "flag": "Bayrak", "font_price": "Fiyat yazı tipi", "font_weight": "Yazı tipi ağırlığı", "font": "Yazı tipi", "full_width_first_image": "Tam g<PERSON>şliğe sahip bi<PERSON>ci gö<PERSON>l", "full_width_on_mobile": "<PERSON>bil c<PERSON>azlarda tam genişlik", "heading_preset": "Başlık ön ayarı", "hide_unselected_variant_media": "Seçimi kaldırılmış varyasyon medyasını gizle", "horizontal_gap": "<PERSON><PERSON><PERSON>", "horizontal_offset": "<PERSON><PERSON><PERSON>", "hover_behavior": "Üzerine gitme da<PERSON>ışı", "icon_background": "Simge arka planı", "icons": "<PERSON><PERSON><PERSON><PERSON>", "image_border_radius": "Görsel köşe yarıçapı", "installments": "<PERSON><PERSON><PERSON><PERSON>", "integrated_button": "<PERSON><PERSON><PERSON><PERSON>", "language_selector": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "large": "Büyük", "left_padding": "Sol dolgu", "left": "Sol", "letter_spacing": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "limit_media_to_screen_height": "Ekran yüksekliğiyle sınırla", "limit_product_details_width": "Ürün bilgileri genişliğini sınırla", "link_preset": "Bağlantı ön ayarı", "links": "Bağlantılar", "logo": "Logo", "loop": "Döngü", "make_details_sticky_desktop": "Masaüstünde sabit", "max_width": "<PERSON><PERSON><PERSON><PERSON> genişlik", "media_height": "<PERSON><PERSON><PERSON>", "media_overlay": "Medya yer paylaşımı", "media_position": "<PERSON><PERSON><PERSON> kon<PERSON>u", "media_type": "<PERSON><PERSON><PERSON>", "media_width": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "mobile_columns": "<PERSON><PERSON>", "mobile_height": "Mobil yükseklik", "mobile_logo_image": "Mobil logo", "mobile_quick_add": "Mobil hızlı ekleme", "motion_direction": "Motion yönü", "motion": "Motion", "movement_direction": "Hareket yönü", "navigation_bar_color_scheme": "Gezinme ç<PERSON>ğu renk <PERSON>ı", "navigation_bar": "<PERSON><PERSON><PERSON><PERSON>", "navigation": "<PERSON><PERSON><PERSON><PERSON>", "open_new_tab": "Bağlantıyı yeni sekmede aç", "overlay_color": "<PERSON><PERSON><PERSON><PERSON><PERSON>ngi", "overlay": "<PERSON><PERSON>", "padding_bottom": "Alt dolgu", "padding_horizontal": "<PERSON><PERSON><PERSON>", "padding_top": "<PERSON>st dolgu", "page_width": "Say<PERSON> genişliği", "pagination": "<PERSON><PERSON><PERSON><PERSON>", "placement": "<PERSON><PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON>", "preset": "<PERSON><PERSON> a<PERSON>", "product_cards": "<PERSON>rün kartları", "product_pages": "<PERSON><PERSON><PERSON><PERSON>", "product_templates": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quick_add": "Hızlı ekle", "ratio": "<PERSON><PERSON>", "regular": "Normal", "review_count": "Değerlendirme sayısı", "right": "Sağ", "row_height": "Sa<PERSON>ır yü<PERSON>ği", "row": "Satır", "seller_note": "Satıcı için nota izin ver", "shape": "Şekil", "show_as_accordion": "<PERSON><PERSON> c<PERSON> akordeon olarak gö<PERSON>", "show_sale_price_first": "Önce indirimli fiyatı göster", "show_tax_info": "<PERSON><PERSON>gi bi<PERSON>i", "show": "<PERSON><PERSON><PERSON>", "small": "Küçük", "speed": "Hız", "statement": "<PERSON><PERSON><PERSON>", "sticky_header": "Sabit üstbilgi", "text_hierarchy": "<PERSON><PERSON>", "text_presets": "<PERSON>in ön a<PERSON>ları", "title": "Başlık", "top": "Üst", "type_preset": "<PERSON>in ön ayarı", "type": "<PERSON><PERSON><PERSON>", "underline_thickness": "Alt çizgi kalınlığı", "variant_images": "Varyasyon <PERSON>", "vendor": "Satıcı", "vertical_gap": "<PERSON><PERSON> b<PERSON>", "vertical_offset": "<PERSON><PERSON><PERSON> dikey den<PERSON>", "vertical_on_mobile": "<PERSON><PERSON> c<PERSON> dikey", "view_all_as_last_card": "Son kart olarak \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON>\"", "weight": "Ağırlık", "wrap": "Kaydır", "shadow_opacity": "<PERSON><PERSON><PERSON>", "show_filter_label": "Uygulanan filtreler i<PERSON><PERSON> metin et<PERSON>i", "show_swatch_label": "<PERSON>umune <PERSON> i<PERSON><PERSON> metin et<PERSON>i", "always_stack_buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON> her zaman yığ", "transparent_background": "Şeffaf arka plan", "gradient_direction": "<PERSON><PERSON>", "overlay_style": "<PERSON><PERSON><PERSON><PERSON><PERSON> stili", "custom_mobile_width": "Özel mobil ekran genişliği", "read_only": "Salt okunur", "headings": "Başlıklar", "horizontal_padding": "<PERSON><PERSON><PERSON>", "show_count": "Sayıyı göster", "vertical_padding": "<PERSON><PERSON> dolgu", "visibility": "Görünürlük", "account": "<PERSON><PERSON><PERSON>", "align_baseline": "<PERSON><PERSON> taban <PERSON> hi<PERSON>a", "add_discount_code": "<PERSON><PERSON> indirimlere izin ver", "background_overlay": "Arka plan yer paylaşımı", "background_media": "Arka plan medyası", "border_thickness": "Kenarlık kalınlığı", "bottom_row": "Alt satır", "button_text_case": "<PERSON><PERSON> b<PERSON>/küç<PERSON>k harfi", "button_text_weight": "<PERSON><PERSON>", "auto_open_cart_drawer": "\"Sepete ekle\", çekmeceyi otomatik olarak açar", "collection_count": "Koleksiyon sayısı", "custom_liquid": "Liquid kodu", "default": "Varsayılan", "default_logo": "Varsayılan logo", "divider_width": "Ayırıcı genişliği", "hide_logo_on_home_page": "<PERSON> sayfada logoyu gizle", "inverse": "<PERSON><PERSON>", "inverse_logo": "Ters logo", "layout_style": "Stil", "length": "Uzunluk", "mobile_pagination": "Mobil cihazlar iç<PERSON> a<PERSON>", "open_row_by_default": "Varsayılan olarak satırı aç", "page_transition_enabled": "Sayfa geçişi", "search": "Ara", "search_icon": "<PERSON><PERSON> si<PERSON>", "search_position": "<PERSON><PERSON>", "search_row": "Satır", "show_author": "<PERSON><PERSON>", "show_alignment": "Hizalamayı göster", "show_date": "<PERSON><PERSON><PERSON>", "show_pickup_availability": "<PERSON><PERSON><PERSON> alım için stok durumunu göster", "show_search": "Aramayı göster", "use_inverse_logo": "Ters logo kullan", "product_corner_radius": "Ürün köşe yarıçapı", "card_corner_radius": "Kart köşe yarıçapı", "alignment_mobile": "<PERSON><PERSON>", "animation_repeat": "Animasyonu tekrarla", "blurred_reflection": "Bulanık yansıma", "card_hover_effect": "Kart üzerine gelme efekti", "card_size": "<PERSON>rt boyutu", "collection_title_case": "Koleksiyon başlığı büyük/küçük harf durumu", "effects": "<PERSON><PERSON><PERSON><PERSON>", "inventory_threshold": "Düşük stok eşiği", "mobile_card_size": "Mobil kart boyutu", "page": "Say<PERSON>", "product_and_card_title_case": "Ürün ve kart başlığı büyük/küçük harf durumu", "product_title_case": "Ürün başlığı büyük/küçük harf durumu", "reflection_opacity": "Yansıma opaklığı", "right_padding": "Sağ iç boşluk", "show_inventory_quantity": "Düşük stok miktarını göster", "text_label_case": "<PERSON><PERSON> et<PERSON> b<PERSON>/küç<PERSON>k harf durumu", "transition_to_main_product": "<PERSON>rün kartı - ürün sayfası geçişi", "media": "<PERSON><PERSON><PERSON>", "product_card_carousel": "Döngüyü gö<PERSON>", "show_second_image_on_hover": "Üzerine gelindiğinde ikinci görseli göster", "media_fit": "<PERSON><PERSON><PERSON>", "scroll_speed": "<PERSON><PERSON><PERSON> du<PERSON> kaydır", "show_powered_by_shopify": "\"Shopify tarafından desteklenmektedir\" ibaresini göster", "gift_card_form": "Hediye kartı formu"}, "options": {"adapt_to_image": "<PERSON><PERSON><PERSON><PERSON>", "apple": "<PERSON><PERSON>", "arrow": "Ok", "banana": "Muz", "bottle": "Şişe", "box": "<PERSON><PERSON>", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carrot": "<PERSON><PERSON><PERSON>", "center": "Orta", "chat_bubble": "<PERSON><PERSON><PERSON> balonu", "clipboard": "Pan<PERSON>", "contain": "<PERSON><PERSON> et", "counter": "<PERSON><PERSON><PERSON>", "cover": "Kapak", "custom": "<PERSON><PERSON>", "dairy_free": "Süt ürünü içermez", "dairy": "<PERSON><PERSON><PERSON> ürü<PERSON>", "dropdowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dots": "Noktalar", "dryer": "<PERSON><PERSON><PERSON><PERSON>", "end": "Bitiş", "eye": "Göz", "facebook": "Facebook", "fire": "Ateş", "gluten_free": "Glütensiz", "heart": "<PERSON><PERSON><PERSON>", "horizontal": "<PERSON><PERSON><PERSON>", "instagram": "Instagram", "iron": "Ütü", "large": "Büyük", "leaf": "<PERSON><PERSON><PERSON>", "leather": "<PERSON><PERSON>", "lightning_bolt": "Ş<PERSON>şek", "lipstick": "<PERSON><PERSON><PERSON>", "lock": "<PERSON><PERSON>", "map_pin": "<PERSON><PERSON> pini", "medium": "Orta", "none": "Yok", "numbers": "<PERSON><PERSON><PERSON><PERSON>", "nut_free": "Kabuklu yemişsiz", "pants": "Pantolon", "paw_print": "<PERSON><PERSON>", "pepper": "<PERSON><PERSON>", "perfume": "Parfüm", "pinterest": "Pinterest", "plane": "Uçak", "plant": "<PERSON><PERSON>", "price_tag": "<PERSON><PERSON><PERSON>", "question_mark": "<PERSON><PERSON>", "recycle": "<PERSON><PERSON>", "return": "İade", "ruler": "Cetvel", "serving_dish": "<PERSON><PERSON>", "shirt": "Gömlek", "shoe": "Ayakkabı", "silhouette": "<PERSON><PERSON><PERSON><PERSON>", "small": "Küçük", "snapchat": "Snapchat", "snowflake": "<PERSON><PERSON>", "star": "Yıld<PERSON>z", "start": "Başlangıç", "stopwatch": "Kronometre", "tiktok": "TikTok", "truck": "<PERSON><PERSON><PERSON>", "tumblr": "Tumblr", "twitter": "X (Twitter)", "vertical": "<PERSON><PERSON>", "vimeo": "Vimeo", "washing": "<PERSON><PERSON><PERSON><PERSON>", "auto": "Otomatik", "default": "Varsayılan", "fill": "<PERSON><PERSON><PERSON>", "fit": "Sığdır", "full": "Tam", "full_and_page": "Tam arka plan, sayfa genişliğinde içerik", "heading": "Başlık", "landscape": "<PERSON><PERSON><PERSON>", "lg": "LG", "link": "Bağlantı", "lowercase": "küçük harf", "m": "M", "outline": "<PERSON><PERSON><PERSON>", "page": "Say<PERSON>", "portrait": "Portre", "s": "S", "sentence": "<PERSON><PERSON><PERSON><PERSON>", "solid": "Sabit", "space_between": "Aradaki boşluk", "square": "<PERSON><PERSON>", "uppercase": "Büyük harf", "circle": "Daire", "swatches": "<PERSON><PERSON><PERSON>", "full_and_page_offset_left": "Tam arka plan, <PERSON><PERSON> genişliğinde içerik, soldan dengeleme", "full_and_page_offset_right": "Tam arka plan, <PERSON><PERSON> genişliğinde içerik, sa<PERSON><PERSON>", "offset_left": "<PERSON><PERSON>", "offset_right": "<PERSON><PERSON><PERSON>", "page_center_aligned": "<PERSON><PERSON>, ortaya hizalanmış", "page_left_aligned": "<PERSON><PERSON>, sola hizalanmış", "page_right_aligned": "<PERSON><PERSON>, sağa hizalanmış", "button": "<PERSON><PERSON><PERSON><PERSON>", "caption": "Alt yazı", "h1": "1. Başlık", "h2": "2. Başl<PERSON>k", "h3": "3. Baş<PERSON><PERSON>k", "h4": "4. <PERSON>ş<PERSON><PERSON><PERSON>", "h5": "5. <PERSON><PERSON><PERSON><PERSON><PERSON>", "h6": "6. <PERSON><PERSON><PERSON><PERSON><PERSON>", "paragraph": "Paragra<PERSON>", "primary": "Birincil", "secondary": "İkincil", "tertiary": "Üçüncül", "chevron_left": "Sola ok", "chevron_right": "Sağa ok", "diamond": "Baklava", "grid": "Izgara", "parallelogram": "Paralelkenar", "rounded": "Yuvarlanmış", "fit_content": "Sığdır", "pills": "Seçenekler", "heavy": "<PERSON><PERSON><PERSON><PERSON>", "thin": "İnce", "drawer": "Çekmece", "preview": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON>", "video_uploaded": "Yüklendi", "video_external_url": "Harici URL", "up": "Yukarı", "down": "Aşağı", "gradient": "<PERSON><PERSON>", "fixed": "Sabit", "pixel": "<PERSON><PERSON><PERSON>", "percent": "<PERSON><PERSON>z<PERSON>", "aspect_ratio": "En-boy oranı", "above_carousel": "Carousel'in üzerinde", "all": "Tümü", "always": "Her zaman", "arrows_large": "Büyük oklar", "arrows": "<PERSON><PERSON>", "balance": "Bakiye", "bento": "<PERSON><PERSON>", "black": "Siyah", "bluesky": "<PERSON><PERSON>", "body_large": "Gövde (Büyük)", "body_regular": "<PERSON><PERSON><PERSON><PERSON> (Normal)", "body_small": "Gövde (Küçük)", "bold": "Kalı<PERSON>", "bottom_left": "Sol alt", "bottom_right": "Sağ alt", "bottom": "Alt", "capitalize": "Büyük harf yap", "caret": "Şapka işareti", "carousel": "Carousel", "check_box": "<PERSON><PERSON> kut<PERSON>u", "chevron_large": "Büyük açılı ayraçlar", "chevron": "Açılı ayraç", "chevrons": "Açılı ayraçlar", "classic": "Klasik", "collection_images": "Koleksiyon görselleri", "color": "Renk", "complementary": "Tamamlayıcı", "dissolve": "Ç<PERSON><PERSON><PERSON><PERSON><PERSON>", "dotted": "Noktalı", "editorial": "Başyazı", "extra_large": "Çok büyük", "extra_small": "Çok küçük", "featured_collections": "Öne çıkan koleksiyonlar", "featured_products": "<PERSON><PERSON>", "font_primary": "Birincil", "font_secondary": "İkincil", "font_tertiary": "Üçüncül", "forward": "İleri", "full_screen": "<PERSON>", "heading_extra_large": "Başlık (Çok büyük)", "heading_extra_small": "Başlık (Çok küçük)", "heading_large": "Başlık (Büyük)", "heading_regular": "Başlık (Normal)", "heading_small": "Başlık (Küçük)", "icon": "<PERSON>m<PERSON>", "image": "G<PERSON><PERSON><PERSON>", "input": "<PERSON><PERSON><PERSON>", "inside_carousel": "Carousel'in içinde", "inverse_large": "<PERSON><PERSON> b<PERSON>", "inverse": "<PERSON><PERSON>", "large_arrows": "Büyük oklar", "large_chevrons": "Büyük açılı ayraçlar", "left": "Sol", "light": "Açık", "linkedin": "LinkedIn", "loose": "<PERSON><PERSON><PERSON><PERSON>", "media_first": "<PERSON><PERSON><PERSON><PERSON> medya", "media_second": "<PERSON><PERSON><PERSON> medya", "modal": "Mod", "narrow": "Dar", "never": "<PERSON><PERSON><PERSON>", "next_to_carousel": "Carousel'in yanında", "normal": "Normal", "nowrap": "Kay<PERSON><PERSON>rma yok", "off_media": "<PERSON><PERSON><PERSON> ka<PERSON>", "on_media": "<PERSON><PERSON><PERSON>", "on_scroll_up": "Yukarı kaydırıldığında", "one_half": "1/2", "one_number": "1", "one_third": "1/3", "pill": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "plus": "Plus", "pretty": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "primary_style": "<PERSON><PERSON><PERSON><PERSON> stil", "rectangle": "Dikdörtgen", "regular": "Normal", "related": "Alakalı", "reverse": "<PERSON><PERSON>ev<PERSON>", "rich_text": "<PERSON><PERSON> metin", "right": "Sağ", "secondary_style": "<PERSON><PERSON><PERSON><PERSON> stil", "semibold": "<PERSON><PERSON><PERSON> kalın", "shaded": "<PERSON><PERSON><PERSON><PERSON>", "show_second_image": "İkinci görseli göster", "single": "Tek", "slide_left": "<PERSON>a kaydır", "slide_up": "Yukarı kaydır", "spotify": "Spotify", "stack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text_only": "Yalnızca metin", "threads": "Threads", "thumbnails": "Küçük resimler", "tight": "Sıkı", "top_left": "<PERSON> ü<PERSON>", "top_right": "Sağ üst", "top": "Üst", "two_number": "2", "two_thirds": "2/3", "underline": "Altı çizili", "video": "Video", "wide": "Geniş", "youtube": "YouTube", "below_image": "Görselin altı", "hidden": "<PERSON><PERSON><PERSON>", "on_image": "Görselin üzeri", "spotlight": "Spotlight", "accent": "Vurgu", "body": "<PERSON><PERSON><PERSON><PERSON>", "button_primary": "<PERSON><PERSON><PERSON><PERSON>", "button_secondary": "<PERSON><PERSON><PERSON><PERSON>", "compact": "Kompakt", "crop_to_fit": "Sığacak şekilde kı<PERSON>", "hint": "<PERSON><PERSON><PERSON>", "maintain_aspect_ratio": "En boy oranını koru", "off": "<PERSON><PERSON><PERSON>", "social_bluesky": "Sosyal medya: <PERSON><PERSON>", "social_facebook": "Sosyal medya: Facebook", "social_instagram": "Sosyal medya: Instagram", "social_linkedin": "Sosyal medya: LinkedIn", "social_pinterest": "Sosyal medya: Pinterest", "social_snapchat": "Sosyal medya: <PERSON><PERSON><PERSON><PERSON>", "social_spotify": "Sosyal medya: Spotify", "social_threads": "Sosyal medya: Threads", "social_tiktok": "Sosyal medya: <PERSON><PERSON><PERSON><PERSON>", "social_tumblr": "Sosyal medya: Tumb<PERSON>r", "social_twitter": "Sosyal medya: X (Twitter)", "social_whatsapp": "Sosyal medya: WhatsApp", "social_vimeo": "Sosyal medya: <PERSON><PERSON><PERSON>", "social_youtube": "So<PERSON>al medya: YouTube", "standard": "<PERSON><PERSON>", "subheading": "Alt başlık", "blur": "Bulanıklaştır", "lift": "Yükseltme", "reveal": "<PERSON><PERSON><PERSON>", "scale": "Ölçek", "subtle_zoom": "Yakınlaştırma"}, "content": {"background_video": "Arka plan <PERSON>u", "describe_the_video_for": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "width_is_automatically_optimized": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "advanced": "Gelişmiş", "background_image": "Arka plan resmi", "block_size": "Blok boyutu", "borders": "Kenarlıklar", "section_size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideshow_width": "<PERSON><PERSON><PERSON> genişliği", "typography": "Tipografi", "complementary_products": "Search & Discovery uygulaması kullanılarak tamamlayıcı ürünler ayarlanmalıdır. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery)", "mobile_column_optimization": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir", "content_width": "İçerik genişliği yalnızca bölüm genişliği tam genişliğe ayarlandığında uygulanır.", "adjustments_affect_all_content": "Bu bloktaki tüm içeriklere uygulanır", "responsive_font_sizes": "<PERSON><PERSON><PERSON>, tüm ekran boyutlarına göre otomatik olarak ölçeklenir", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swatches": "<PERSON><PERSON><PERSON>", "variant_settings": "Varyasyon a<PERSON>ları", "background": "Arka plan", "cards_layout": "<PERSON><PERSON><PERSON>", "section_layout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile_size": "<PERSON><PERSON>", "appearance": "G<PERSON>rü<PERSON><PERSON><PERSON>", "arrows": "<PERSON><PERSON>", "body_size": "<PERSON><PERSON><PERSON><PERSON>", "bottom_row_appearance": "Alt satır görünümü", "carousel_navigation": "Carousel gez<PERSON><PERSON>i", "carousel_pagination": "Carousel say<PERSON><PERSON><PERSON> a<PERSON>", "copyright": "Telif <PERSON>", "edit_logo_in_theme_settings": "Logonuzu [te<PERSON> a<PERSON>](/editor?context=theme&category=logo%20and%20favicon) bölümünde düzenleyin", "edit_price_in_theme_settings": "<PERSON>yat biçimlendirmesini [tema ayarlar<PERSON>](/editor?context=theme&category=currency%20code) bölümünde düzenleyin", "edit_variants_in_theme_settings": "<PERSON><PERSON><PERSON><PERSON> stilini [tema a<PERSON>](/editor?context=theme&category=variants) bölümünde düzenleyin", "email_signups_create_customer_profiles": "<PERSON><PERSON><PERSON> ekleme [mü<PERSON><PERSON>i profilleri](https://help.shopify.com/manual/customers)", "follow_on_shop_eligiblity": "Düğmenin gösterilmesi için Shop kanalı yüklenmiş ve Shop Pay etkinleştirilmiş olmalıdır. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)", "fonts": "Yazı Tipleri", "grid": "Izgara", "heading_size": "Başlık boyutu", "image": "G<PERSON><PERSON><PERSON>", "input": "<PERSON><PERSON><PERSON>", "layout": "D<PERSON>zen", "link": "Bağlantı", "link_padding": "Bağlantı dolgusu", "localization": "<PERSON><PERSON><PERSON>", "logo": "Logo", "margin": "<PERSON><PERSON>", "media": "<PERSON><PERSON><PERSON>", "media_1": "Medya 1", "media_2": "Medya 2", "menu": "<PERSON><PERSON>", "mobile_layout": "<PERSON><PERSON>", "padding": "<PERSON><PERSON><PERSON>", "padding_desktop": "Masaüstü do<PERSON>gus<PERSON>", "paragraph": "Paragra<PERSON>", "policies": "Politikalar", "popup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "search": "Ara", "size": "<PERSON><PERSON>", "social_media": "So<PERSON>al medya", "submit_button": "<PERSON><PERSON><PERSON>", "text_presets": "<PERSON>in ön a<PERSON>ları", "transparent_background": "Şeffaf arka plan", "typography_primary": "Birincil <PERSON>", "typography_secondary": "İ<PERSON>cil tipografi", "typography_tertiary": "Üçüncül tipografi", "mobile_width": "Mobil ekran genişliği", "width": "Genişlik", "carousel": "Carousel", "colors": "Ren<PERSON>r", "collection_page": "Koleksiyon sayfası", "customer_account": "Müşteri hesabı", "edit_empty_state_collection_in_theme_settings": "Boş durum koleksiyonunu [tema ayar<PERSON>](/editor?context=theme&category=search)] bölümünden düzenleyin", "home_page": "<PERSON>", "images": "<PERSON><PERSON><PERSON><PERSON>", "inverse_logo_info": "Şeffaf üstbilgi arka planı, Ters olarak ayarlandığında kullanılır", "manage_customer_accounts": "Müşteri hesabı ayarlarında [görün<PERSON><PERSON><PERSON><PERSON><PERSON> yönetme](/admin/settings/customer_accounts). Eski hesaplarda desteklenmez.", "manage_policies": "[Politikaları yönetme](/admin/settings/legal)", "product_page": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON>", "thumbnails": "Küçük resimler", "visibility": "Görünürlük", "visible_if_collection_has_more_products": "Koleksiyonda gösterilenden daha fazla ürün varsa görünür", "grid_layout": "<PERSON>zgara düzeni", "app_required_for_ratings": "<PERSON><PERSON><PERSON><PERSON> derecelendirmele<PERSON> i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/apps)", "icon": "<PERSON>m<PERSON>", "manage_store_name": "[Ma<PERSON><PERSON>a adını yönet](/admin/settings/general?edit=storeName)", "resource_reference_collection_card": "<PERSON> b<PERSON><PERSON><PERSON> kole<PERSON><PERSON><PERSON><PERSON>", "resource_reference_collection_card_image": "<PERSON> k<PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_collection_title": "<PERSON> k<PERSON><PERSON> başlı<PERSON><PERSON>ö<PERSON><PERSON><PERSON>", "resource_reference_product": "<PERSON> ürüne otomatik olarak bağlanır", "resource_reference_product_card": "<PERSON> b<PERSON><PERSON><PERSON> ürü<PERSON>ü <PERSON><PERSON><PERSON>", "resource_reference_product_inventory": "Ana üründeki envanteri <PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_price": "Ana üründeki fiyatı g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_recommendations": "<PERSON>ü temel alan ö<PERSON>iler<PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_review": "Ana üründeki değerlendirmeleri görü<PERSON><PERSON>", "resource_reference_product_swatches": "Ana üründeki numune parçaları g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_title": "Ana üründeki başlığ<PERSON> gö<PERSON><PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_variant_picker": "Ana üründeki varyasyonları gör<PERSON><PERSON><PERSON><PERSON>", "resource_reference_product_media": "Ana üründeki medyayı g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product_media": "<PERSON><PERSON><PERSON><PERSON> medyası", "section_link": "Bölüm bağlantısı", "gift_card_form_description": "Müşteriler kişisel mesaj ekleyerek alıcının e-posta adresine hediye kartı gönderebilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/gift-card-products)"}, "html_defaults": {"share_information_about_your": "<p>Müşterilerinizle markanız hakkında bilgi payla<PERSON>ın. <PERSON>r<PERSON>n açı<PERSON> girin, du<PERSON>ru paylaşın veya mağazanıza gelen müşterileri karşılayın.</p>"}, "text_defaults": {"collapsible_row": "Daraltılabilir <PERSON>", "button_label": "<PERSON><PERSON><PERSON> al", "heading": "Başlık", "email_signup_button_label": "<PERSON><PERSON> ol", "accordion_heading": "Akordeon başlığı", "contact_form_button_label": "<PERSON><PERSON><PERSON>", "popup_link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere bağlantısı", "sign_up": "<PERSON><PERSON><PERSON>", "welcome_to_our_store": "Mağazamıza hoş geldiniz", "be_bold": "<PERSON><PERSON><PERSON><PERSON> yazı<PERSON>la cesaretinizi sergileyin.", "shop_our_latest_arrivals": "Yeni gelen ürünleri incele!"}, "info": {"video_alt_text": "Yardımcı teknoloji kullanan kişiler için videonun içeriğini tarif et", "video_autoplay": "<PERSON>lar varsayılan olarak sessize alınır", "video_external": "YouTube veya Vimeo URL'si kullanın", "carousel_layout_on_mobile": "Carousel, mobil cihazlarda kullanılır", "carousel_hover_behavior_not_supported": "\"Carousel\" t<PERSON><PERSON><PERSON>, bölüm seviyesinde seçildiğinde \"Carousel\" üzerine gelerek vurgulama desteği bulunmamaktadır", "link_info": "İsteğe bağlı: simgeyi tıklanabilir hale getirir", "grid_layout_on_mobile": "Mobil için ızgara düzeni kullanılır", "logo_font": "Bir logo seçilmediğinde geçerli olur", "checkout_buttons": "Al<PERSON><PERSON><PERSON><PERSON><PERSON>n daha hızlı ödeme yapmasına olanak sağlar ve dönüşümü artırabilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/dynamic-checkout)", "custom_heading": "<PERSON><PERSON> başlık", "edit_presets_in_theme_settings": "<PERSON><PERSON> a<PERSON> [tema ayarlar<PERSON>](/editor?context=theme&category=typography) bölümünde düzenleyin", "enable_filtering_info": "Filtreleri [Search & Discovery uygulaması](https://help.shopify.com/manual/online-store/search-and-discovery/filters) ile kişiselleştirin", "manage_countries_regions": "[Ülk<PERSON><PERSON>/bölgeleri yönet](/admin/settings/markets)", "manage_languages": "[<PERSON><PERSON><PERSON> y<PERSON>](/admin/settings/languages)", "transparent_background": "Okunabilirlik için şeffaf arka plan uygulanmış her bir şablonu inceleyin", "aspect_ratio_adjusted": "Bazı düzenlerde ayarlandı", "auto_open_cart_drawer": "Etkinleştirildiğinde sepete ürün eklenmesi durumunda sepet çekmecesi otomatik olarak açılır.", "custom_liquid": "Gelişmiş kişiselleştirmeler oluşturmak için uygulama parçacıkları veya başka bir kod ekleyin. [Daha fazla bilgi edinin](https://shopify.dev/docs/api/liquid)", "applies_on_image_only": "Yalnızca görseller için geçerlidir", "hover_effects": "Ürün ve koleksiyon kartları için geçerlidir", "pills_usage": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtrel<PERSON>, indirim kodları ve arama önerileri için kullanılır"}, "categories": {"product_list": "Öne çıkan koleksiyon", "basic": "Basic", "collection": "Koleksiyon", "collection_list": "Koleks<PERSON><PERSON>esi", "footer": "Altbilgi", "forms": "Formlar", "header": "Üstbilgi", "layout": "D<PERSON>zen", "links": "Bağlantılar", "product": "<PERSON><PERSON><PERSON><PERSON>", "banners": "<PERSON>'lar", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON>", "decorative": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other_sections": "<PERSON><PERSON><PERSON>", "storytelling": "Hikaye anlatıcılığı"}}