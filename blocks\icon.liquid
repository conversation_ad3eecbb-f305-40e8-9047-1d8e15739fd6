

{% assign block_settings = block.settings %}
{% assign icon_block_class = 'icon-block__media icon-block-' | append: block.id %}

{% capture icon %}
  {% render 'icon-or-image',
    icon: block_settings.icon,
    image_upload: block_settings.image_upload,
    width: block_settings.width,
    class_name: icon_block_class,
    attributes: block.shopify_attributes
  %}
{% endcapture %}

{%- if block_settings.link != blank -%}
  <a
    href="{{ block_settings.link }}"
    class="text-inherit"
    {% if block_settings.open_in_new_tab %}
      target="_blank" rel="noopener"
    {% endif %}
  >
    {{ icon }}
  </a>
{%- else -%}
  {{ icon }}
{%- endif -%}

{% stylesheet %}
  .icon-block {
    display: flex;
    fill: currentcolor;
    flex-shrink: 0;
  }

  .icon-block__media {
    height: auto;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.icon",
  "class": "icon-block",
  "settings": [
    {
      "type": "select",
      "id": "icon",
      "label": "t:settings.icon",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "apple",
          "label": "t:options.apple"
        },
        {
          "value": "arrow",
          "label": "t:options.arrow"
        },
        {
          "value": "banana",
          "label": "t:options.banana"
        },
        {
          "value": "bottle",
          "label": "t:options.bottle"
        },
        {
          "value": "box",
          "label": "t:options.box"
        },
        {
          "value": "carrot",
          "label": "t:options.carrot"
        },
        {
          "value": "chat_bubble",
          "label": "t:options.chat_bubble"
        },
        {
          "value": "check_box",
          "label": "t:options.check_box"
        },
        {
          "value": "clipboard",
          "label": "t:options.clipboard"
        },
        {
          "value": "dairy",
          "label": "t:options.dairy"
        },
        {
          "value": "dairy_free",
          "label": "t:options.dairy_free"
        },
        {
          "value": "dryer",
          "label": "t:options.dryer"
        },
        {
          "value": "eye",
          "label": "t:options.eye"
        },
        {
          "value": "fire",
          "label": "t:options.fire"
        },
        {
          "value": "gluten_free",
          "label": "t:options.gluten_free"
        },
        {
          "value": "heart",
          "label": "t:options.heart"
        },
        {
          "value": "iron",
          "label": "t:options.iron"
        },
        {
          "value": "leaf",
          "label": "t:options.leaf"
        },
        {
          "value": "leather",
          "label": "t:options.leather"
        },
        {
          "value": "lightning_bolt",
          "label": "t:options.lightning_bolt"
        },
        {
          "value": "lipstick",
          "label": "t:options.lipstick"
        },
        {
          "value": "lock",
          "label": "t:options.lock"
        },
        {
          "value": "map_pin",
          "label": "t:options.map_pin"
        },
        {
          "value": "nut_free",
          "label": "t:options.nut_free"
        },
        {
          "value": "pants",
          "label": "t:options.pants"
        },
        {
          "value": "paw_print",
          "label": "t:options.paw_print"
        },
        {
          "value": "pepper",
          "label": "t:options.pepper"
        },
        {
          "value": "perfume",
          "label": "t:options.perfume"
        },
        {
          "value": "plane",
          "label": "t:options.plane"
        },
        {
          "value": "plant",
          "label": "t:options.plant"
        },
        {
          "value": "price_tag",
          "label": "t:options.price_tag"
        },
        {
          "value": "question_mark",
          "label": "t:options.question_mark"
        },
        {
          "value": "recycle",
          "label": "t:options.recycle"
        },
        {
          "value": "return",
          "label": "t:options.return"
        },
        {
          "value": "ruler",
          "label": "t:options.ruler"
        },
        {
          "value": "serving_dish",
          "label": "t:options.serving_dish"
        },
        {
          "value": "shirt",
          "label": "t:options.shirt"
        },
        {
          "value": "shoe",
          "label": "t:options.shoe"
        },
        {
          "value": "silhouette",
          "label": "t:options.silhouette"
        },
        {
          "value": "bluesky",
          "label": "t:options.social_bluesky"
        },
        {
          "value": "facebook",
          "label": "t:options.social_facebook"
        },
        {
          "value": "instagram",
          "label": "t:options.social_instagram"
        },
        {
          "value": "linkedin",
          "label": "t:options.social_linkedin"
        },
        {
          "value": "pinterest",
          "label": "t:options.social_pinterest"
        },
        {
          "value": "snapchat",
          "label": "t:options.social_snapchat"
        },
        {
          "value": "spotify",
          "label": "t:options.social_spotify"
        },
        {
          "value": "threads",
          "label": "t:options.social_threads"
        },
        {
          "value": "tiktok",
          "label": "t:options.social_tiktok"
        },
        {
          "value": "tumblr",
          "label": "t:options.social_tumblr"
        },
        {
          "value": "twitter",
          "label": "t:options.social_twitter"
        },
        {
          "value": "vimeo",
          "label": "t:options.social_vimeo"
        },
        {
          "value": "youtube",
          "label": "t:options.social_youtube"
        },
        {
          "value": "whatsapp",
          "label": "t:options.social_whatsapp"
        },
        {
          "value": "snowflake",
          "label": "t:options.snowflake"
        },
        {
          "value": "star",
          "label": "t:options.star"
        },
        {
          "value": "stopwatch",
          "label": "t:options.stopwatch"
        },
        {
          "value": "truck",
          "label": "t:options.truck"
        },
        {
          "value": "washing",
          "label": "t:options.washing"
        }
      ],
      "default": "apple"
    },
    {
      "type": "image_picker",
      "id": "image_upload",
      "label": "t:settings.image_icon"
    },
    {
      "type": "range",
      "id": "width",
      "label": "t:settings.width",
      "min": 12,
      "max": 200,
      "step": 2,
      "unit": "px",
      "default": 24
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    },
    {
      "type": "checkbox",
      "id": "open_in_new_tab",
      "label": "t:settings.open_new_tab",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:names.icon",
      "category": "t:categories.basic",
      "settings": {
        "icon": "price_tag"
      }
    }
  ]
}
{% endschema %}
