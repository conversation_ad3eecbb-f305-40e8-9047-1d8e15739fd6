/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "collection_list_Y66Rtq": {
      "type": "main-collection-list",
      "blocks": {
        "group_iWXpre": {
          "type": "group",
          "name": "t:names.header",
          "settings": {
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 16,
            "width": "fit-content",
            "custom_width": 100,
            "width_mobile": "fit-content",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "placeholder": "",
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_xpXCCq": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<h3>Shop by collection</h3>",
                "width": "100%",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h2",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 16,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_xpXCCq"
          ]
        },
        "static-collection-card": {
          "type": "_collection-card",
          "name": "t:names.collection_card",
          "static": true,
          "settings": {
            "placement": "on_image",
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "flex-start",
            "collection_card_gap": 8,
            "inherit_color_scheme": true,
            "color_scheme": "scheme-6",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0
          },
          "blocks": {
            "collection_title_xLppmy": {
              "type": "collection-title",
              "name": "t:names.collection_title",
              "settings": {
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "collection-card-image": {
              "type": "_collection-card-image",
              "name": "t:names.collection_card_image",
              "static": true,
              "settings": {
                "image_ratio": "portrait",
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0
              },
              "blocks": {}
            }
          },
          "block_order": [
            "collection_title_xLppmy"
          ]
        }
      },
      "block_order": [
        "group_iWXpre"
      ],
      "name": "Collection list",
      "settings": {
        "layout_type": "grid",
        "carousel_on_mobile": true,
        "columns": 3,
        "mobile_columns": "2",
        "columns_gap": 1,
        "bento_gap": 8,
        "rows_gap": 1,
        "max_collections": 4,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "gap": 24,
        "color_scheme": "scheme-1",
        "padding-block-start": 60,
        "padding-block-end": 60
      }
    }
  },
  "order": [
    "collection_list_Y66Rtq"
  ]
}