{"blocks": {"load_video": "<PERSON><PERSON><PERSON> betölt<PERSON>: {{ description }}", "sold_out": "Elfogyott", "email_signup": {"label": "E-mail-cím", "placeholder": "E-mail-cím", "success": "Köszönjük feliratkozást!"}, "filter": "Szűrés", "payment_methods": "Fizetési m<PERSON>", "contact_form": {"name": "Név", "email": "E-mail-cím", "phone": "Telefonszám", "comment": "Hozzászólás", "post_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy írtál nekünk. A lehető legrövidebb időn belül válaszolni fogunk.", "error_heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, helyesbítsd a következőket:"}}, "accessibility": {"play_model": "3D-model<PERSON>", "play_video": "<PERSON><PERSON><PERSON>", "unit_price": "<PERSON>gy<PERSON><PERSON><PERSON><PERSON><PERSON>", "country_results_count": "{{ count }} ta<PERSON><PERSON><PERSON>", "slideshow_pause": "Diavetítés megállítása", "slideshow_play": "Diavetítés indítása", "remove_item": "{{ title}} eltávolítása", "skip_to_text": "Ugrás a tartalomhoz", "skip_to_product_info": "<PERSON><PERSON><PERSON><PERSON>, és ugrás a termékadatokra", "skip_to_results_list": "Ugrás a találati listára", "new_window": "Új ablakban nyílik meg.", "slideshow_next": "Következő dia", "slideshow_previous": "Előző dia", "close_dialog": "Párbeszédablak bezárása", "reset_search": "Keresés alaphelyzetbe állítása", "search_results_count": "{{ count }} tal<PERSON><PERSON> a(z) „{{ query }}” kifejezésre", "search_results_no_results": "<PERSON><PERSON><PERSON> ta<PERSON> a(z) „{{ query }}” kifejezésre", "filters": "Szűrők", "filter_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON> alkalmazva", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON> alkalmazva"}, "account": "Fiókmenü me<PERSON>", "cart": "<PERSON><PERSON><PERSON><PERSON>", "cart_count": "Összes termék a kosárban", "menu": "<PERSON><PERSON>", "country_region": "Ország/régió", "slide_status": "{{ index }}./{{ length }} dia", "scroll_to": "<PERSON><PERSON><PERSON><PERSON> ide: {{ title }}", "loading_product_recommendations": "Termékajánlások betöltése", "discount": "Kedvez<PERSON><PERSON><PERSON><PERSON><PERSON>", "discount_applied": "<PERSON><PERSON><PERSON><PERSON><PERSON>vezménykód: {{ code }}", "open_cart_drawer": "<PERSON><PERSON><PERSON><PERSON>", "inventory_status": "<PERSON><PERSON><PERSON><PERSON>", "pause_video": "<PERSON><PERSON><PERSON>", "find_country": "Orsz<PERSON>g k<PERSON>", "localization_region_and_language": "Régió- és nyelvválasztó megnyitása", "open_search_modal": "<PERSON><PERSON><PERSON>", "decrease_quantity": "Mennyiség csökkentése", "increase_quantity": "Mennyiség növelése", "quantity": "Mennyiség", "rating": "A termék értékelése: {{ rating }} / 5", "nested_product": "{{ product_title }} – {{ parent_title }}"}, "actions": {"add_to_cart": "Hozzáadás a kosárhoz", "clear_all": "Az összes törléses", "remove": "Eltávolítás", "view_in_your_space": "Megtekintés a saját környezetben", "show_filters": "Szűrés", "clear": "Törlés", "continue_shopping": "Vásárlás folytat<PERSON>", "log_in_html": "<PERSON><PERSON><PERSON>? <a href=\"{{ link }}\">Jelentkezz be</a> a gyorsabb fizetéshez.", "see_items": {"one": "{{ count }} term<PERSON><PERSON> me<PERSON>", "other": "{{ count }} term<PERSON><PERSON> me<PERSON>"}, "view_all": "Az összes megtekintése", "add": "Hozzáadás", "choose": "Kiválasztás", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show_less": "<PERSON><PERSON><PERSON><PERSON>", "show_more": "<PERSON><PERSON><PERSON>", "close": "Bezárás", "more": "<PERSON><PERSON><PERSON><PERSON>", "zoom": "Nagyítás", "close_dialog": "Párbeszédablak bezárása", "reset": "Alaphelyzetbe állítás", "enter_using_password": "Belépés j<PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enter_password": "Jelszó megadása", "back": "<PERSON><PERSON><PERSON>", "log_in": "Bejelentkezés", "log_out": "Kijelentkezés", "remove_discount": "Ke<PERSON>vezménykód ({{ code }}) eltávolítása", "view_store_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sign_in_options": "Tovább<PERSON> be<PERSON>zési lehetőségek", "sign_up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "open_image_in_full_screen": "<PERSON><PERSON><PERSON> me<PERSON>ása teljes k<PERSON>n", "sort": "Rendez<PERSON>", "show_all_options": "Összes lehetőség megjelenítése"}, "content": {"reviews": "összegzés", "no_results_found": "<PERSON><PERSON><PERSON>", "language": "Nyelv", "localization_region_and_language": "Régió és nyelv", "cart_total": "Kosár végösszege", "your_cart_is_empty": "A kosarad üres", "product_image": "Termékkép", "product_information": "Termékadatok", "quantity": "Mennyiség", "product_total": "Termék végösszege", "cart_estimated_total": "Becsült végösszeg", "seller_note": "Különleges utasítások", "cart_subtotal": "Részösszeg", "discounts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat és az adókat. A kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "Tartalmazza a vámokat és az adókat. A kedvezmények és a szállítási költség kiszámítása a pénztárban történik.", "taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza az adókat. A kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.", "taxes_included_shipping_at_checkout_without_policy": "Tartalmazza az adókat. A kedvezmények és a szállítási költség kiszámítása a pénztárban történik.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat. <PERSON><PERSON>, a kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Tartalmazza a vámokat. <PERSON><PERSON>, a kedvezmények és a szállítási költség kiszámítása a pénztárban történik.", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON>, a kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik..", "taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON>, a kedvezmények és a szállítási költség kiszámítása a pénztárban történik.", "cart_title": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "price_regular": "<PERSON><PERSON><PERSON><PERSON>", "price_compare_at": "Összehasonlí<PERSON><PERSON>", "price_sale": "<PERSON><PERSON><PERSON><PERSON>", "checkout": "<PERSON><PERSON><PERSON><PERSON>", "duties_and_taxes_included": "Tartalmazza a vámokat és az adókat.", "duties_included": "Tartalmazza a vámokat.", "shipping_policy_html": "A fizetéskor kiszámított <a href=\"{{ link }}\">szállítási költség</a>.", "taxes_included": "Tartalmazza az adókat.", "product_badge_sold_out": "Elfogyott", "product_badge_sale": "<PERSON><PERSON><PERSON><PERSON>", "grid_view": {"default_view": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "grid_fieldset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single_item": "<PERSON><PERSON><PERSON><PERSON>", "zoom_out": "Felnagyítás"}, "search_input_label": "Keresés", "search_input_placeholder": "Keresés", "search_results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search_results_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search_results_no_results": "<PERSON><PERSON><PERSON> ta<PERSON> erre: „{{ terms }}”. Próbálj meg másra rákere<PERSON>ni.", "search_results_resource_articles": "Blogbejegyzések", "search_results_resource_collections": "Kollekciók", "search_results_resource_pages": "<PERSON><PERSON>", "search_results_resource_products": "Termékek", "search_results_resource_queries": "Javaslatok keresése", "search_results_view_all": "Összes megtekintése", "search_results_view_all_button": "Összes megtekintése", "search_results_resource_products_count": {"one": "{{ count }} term<PERSON>k", "other": "{{ count }} term<PERSON>k"}, "recently_viewed_products": "Nemrégiben me<PERSON>", "unavailable": "<PERSON><PERSON><PERSON>", "collection_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "product_card_placeholder": "Termék c<PERSON>", "product_count": "Termékek száma", "item_count": {"one": "{{ count }} term<PERSON>k", "other": "{{ count }} term<PERSON>k"}, "errors": "<PERSON><PERSON><PERSON>", "price_from": "Indulóár: {{ price }}", "featured_products": "<PERSON><PERSON><PERSON><PERSON>", "no_products_found": "<PERSON><PERSON><PERSON>.", "use_fewer_filters_html": "Pr<PERSON>b<PERSON><PERSON><PERSON> meg k<PERSON> s<PERSON><PERSON>, vagy <a class=\"{{ class }}\" href=\"{{ link }}\">tör<PERSON>ld az összes szűrőt</a>.", "search": "Keresés", "search_results_no_results_check_spelling": "<PERSON><PERSON><PERSON> ta<PERSON> erre: „{{ terms }}”. <PERSON><PERSON><PERSON><PERSON> a <PERSON><PERSON><PERSON><PERSON><PERSON>, vagy írj be egy másik szót vagy k<PERSON>.", "filters": "Szűrők", "price_filter_html": "A legmagasabb ár {{ price }}", "blog_details_separator": "|", "read_more": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "wrong_password": "<PERSON><PERSON><PERSON>", "account_title": "<PERSON>ók", "account_title_personalized": "Ked<PERSON> {{ first_name }}!", "account_orders": "<PERSON><PERSON><PERSON><PERSON>", "account_profile": "Profil", "discount_code": "<PERSON><PERSON><PERSON>ód", "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza a vámokat és az adókat. A szállítási díjat a pénztárnál számítjuk ki.", "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza a vámokat és az adókat. A szállítási díjat a pénztárnál számítjuk ki.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza a vámokat. A szállítási díjat a pénztárnál számítjuk ki.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza a vámokat. A szállítási díjat a pénztárnál számítjuk ki.", "pickup_available_at_html": "Sze<PERSON><PERSON>esen átvehető itt: <b>{{ location }}</b>", "pickup_available_in": "<PERSON>ze<PERSON><PERSON>esen átvehető ekkor: {{ pickup_time }}", "pickup_not_available": "Személyes átvétel jelenleg nem érhető el", "pickup_ready_in": "{{ pickup_time }}", "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Az adókat és a <a href=\"{{ link }}\">szállítási díjat</a> a pénztárnál számítjuk ki.", "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Az adókat és a szállítási díjat a pénztárnál számítjuk ki.", "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza az adókat. A szállítási díjat a pénztárnál számítjuk ki.", "taxes_included_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza az adókat. A szállítási díjat a pénztárnál számítjuk ki.", "view_more_details": "További részletek megtekintése", "inventory_low_stock": "<PERSON><PERSON><PERSON><PERSON>", "inventory_in_stock": "<PERSON><PERSON><PERSON><PERSON>", "inventory_out_of_stock": "<PERSON><PERSON><PERSON>", "page_placeholder_title": "<PERSON><PERSON> címe", "page_placeholder_content": "<PERSON><PERSON><PERSON><PERSON><PERSON> ki egy oldalt a tartalma megjelenítéséhez.", "placeholder_image": "<PERSON><PERSON><PERSON><PERSON><PERSON> kép", "inventory_low_stock_show_count": {"one": "Elérhető összeg: {{ count }}", "other": "Elérhető összeg: {{ count }}"}, "shipping_policy": "A fizetéskor kiszámított szállítási költség.", "discount_code_error": "A kedvezménykód nem érvényesíthető a kosaradon", "shipping_discount_error": "A szállítási kedvezmények a fizetéskor jelennek meg a cím megadását követően", "powered_by": "A bolt szolgáltató<PERSON>:", "store_owner_link_html": "Te vagy az <PERSON> t<PERSON>? <a href=\"{{ link }}\">Jelentkezz be itt</a>", "recipient_form_send_to": "Címzett", "recipient_form_email_label": "Címzett e-mail-címe", "recipient_form_email_label_my_email": "Az e-mail-címem", "recipient_form_email_label_optional_for_no_js_behavior": "Címzett e-mail-címe (nem kötelező)", "recipient_form_email_address": "Címzett e-mail-címe", "recipient_form_name_label": "Címzett neve (nem kötelező)", "recipient_form_message_label": "Üzenet (nem kötelező)", "recipient_form_message": "Üzenet (nem kötelező)", "recipient_form_characters_used": "{{ max_chars }} karakterből {{ used_chars }} fel<PERSON>ználva", "recipient_form_send_on": "ÉÉÉÉ-HH-NN", "recipient_form_send_on_label": "<PERSON><PERSON><PERSON><PERSON> (nem kötelező)"}, "gift_cards": {"issued": {"how_to_use_gift_card": "Az ajándékkártya kódja online, a QR-kód pedig az üzletben használható fel", "title": "Íme a(z) {{ shop }} üzletben levásárolható, {{ value }} értékű ajándékkártyád!", "subtext": "<PERSON>j<PERSON><PERSON>ékk<PERSON><PERSON><PERSON>", "shop_link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add_to_apple_wallet": "Hozzáadás az Apple Wallethoz", "qr_image_alt": "Ezt a QR-kódot beszkennelve beválthatod az ajándékkártyát.", "copy_code": "Ajándékkártya kódjának másolása", "expiration_date": "<PERSON><PERSON><PERSON><PERSON>: {{ expires_on }}", "copy_code_success": "Sikeres volt a kód másolása", "expired": "<PERSON><PERSON><PERSON><PERSON>"}}, "placeholders": {"password": "Je<PERSON><PERSON><PERSON>", "search": "Keresés", "product_title": "Termék me<PERSON>vezése", "collection_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "products": {"product": {"add_to_cart": "Hozzáadás a kosárhoz", "added_to_cart": "Hozzáadva a kosárhoz", "adding_to_cart": "Hozzáadás…", "add_to_cart_error": "Nem tudtuk hozzáadni a terméket a kosárhoz", "sold_out": "Elfogyott", "unavailable": "<PERSON><PERSON><PERSON>"}}, "fields": {"separator": "–"}, "blogs": {"article": {"comment_author_separator": "•", "comments_heading": {"one": "{{ count }} hozzászólás", "other": "{{ count }} hozzászólás"}}, "comment_form": {"email": "E‑mail-cím", "error": "A hozzászólás közzététele nem si<PERSON>ült, k<PERSON><PERSON><PERSON><PERSON><PERSON>, ügyelj a következőkre:", "heading": "Hozzászólás írása", "message": "Üzenet", "moderated": "Felhívjuk a figyelmedet, hogy közzététel előtt a hozzászólásokat jóvá kell hagyni.", "name": "Név", "post": "Hozzászólás elküldése", "success_moderated": "Hozzászólás közzétéve, moderálásra vár", "success": "Hozzászólás közzétéve"}}, "pagefly": {"products": {"product": {"regular_price": "Regular price", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to cart", "back_to_collection": "Back to {{ title }}", "view_details": "View details"}}, "article": {"tags": "Tags:", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}, "password_page": {"login_form_message": "Enter store using password:", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "password_link": "Enter using password"}}}