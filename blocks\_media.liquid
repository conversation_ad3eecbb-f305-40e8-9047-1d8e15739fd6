

{% render 'media', section_id: section.id %}

{% schema %}
{
  "name": "t:names.media",
  "tag": null,
  "settings": [
    {
      "type": "select",
      "id": "media_type",
      "label": "t:settings.type",
      "options": [
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:settings.image",
      "visible_if": "{{ block.settings.media_type == 'image' }}"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link",
      "visible_if": "{{ block.settings.media_type == 'image'  }}"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:settings.video",
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "video_loop",
      "label": "t:settings.loop",
      "default": true,
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "video_autoplay",
      "label": "t:settings.autoplay",
      "default": false,
      "visible_if": "{{ block.settings.media_type == 'video' }}"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.media"
    }
  ]
}
{% endschema %}
