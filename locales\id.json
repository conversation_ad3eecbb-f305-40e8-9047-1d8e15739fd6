{"blocks": {"load_video": "Muat video: {{ description }}", "sold_out": "Habis", "email_signup": {"label": "Email", "placeholder": "<PERSON><PERSON><PERSON> email", "success": "<PERSON><PERSON> kasih sudah berlang<PERSON>an!"}, "filter": "Filter", "payment_methods": "<PERSON><PERSON>", "contact_form": {"name": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "comment": "Komentar", "post_success": "<PERSON><PERSON> kasih sudah menghubungi kami. Kami akan segera menghubungi Anda.", "error_heading": "<PERSON><PERSON>:"}}, "accessibility": {"play_model": "Putar model 3D", "play_video": "Putar video", "unit_price": "<PERSON><PERSON> satuan", "country_results_count": "{{ count }} hasil", "slideshow_pause": "Jeda slideshow", "slideshow_play": "Putar slideshow", "remove_item": "Ha<PERSON> {{ title}}", "skip_to_text": "Langsung ke konten", "skip_to_product_info": "Langsung ke informasi produk", "skip_to_results_list": "Langsung ke daftar hasil", "new_window": "Membuka di jendela baru.", "close_dialog": "Tutup dialog", "reset_search": "Reset pencarian", "search_results_count": "{{ count }} hasil pencarian ditemukan untuk \"{{ query }}\"", "search_results_no_results": "<PERSON><PERSON>k ditemukan hasil untuk \"{{ query }}\"", "slideshow_next": "Slide berikutnya", "slideshow_previous": "Slide sebelumnya", "filters": "Filter", "account": "Buka menu akun", "cart": "Keranjang", "cart_count": "Total item di keranjang", "filter_count": {"one": "{{ count }} filter diterapkan", "other": "{{ count }} filter diterapkan"}, "menu": "<PERSON><PERSON>", "country_region": "Negara/Wilayah", "slide_status": "Slide {{ index }} dari {{ length }}", "scroll_to": "<PERSON><PERSON><PERSON> ke {{ title }}", "loading_product_recommendations": "Memuat rekomendasi produk", "discount": "<PERSON>ai kode diskon", "discount_applied": "Kode diskon yang dipakai: {{ code }}", "open_cart_drawer": "<PERSON><PERSON> troli", "inventory_status": "Status Inventaris", "pause_video": "Jeda video", "find_country": "Temukan negara", "localization_region_and_language": "<PERSON><PERSON> selektor wilayah dan bahasa", "open_search_modal": "<PERSON><PERSON>", "decrease_quantity": "<PERSON><PERSON><PERSON> jumlah", "increase_quantity": "Tambah jumlah", "quantity": "<PERSON><PERSON><PERSON>", "rating": "Peringkat produk ini adalah {{ rating }} dari 5", "nested_product": "{{ product_title }} untuk {{ parent_title }}"}, "actions": {"add_to_cart": "Tambahkan ke keranjang", "clear_all": "<PERSON><PERSON> semua", "remove": "Hapus", "view_in_your_space": "Lihat di lokasi Anda", "show_filters": "Filter", "clear": "Kosongkan", "continue_shopping": "Lanju<PERSON><PERSON> belanja", "log_in_html": "Sudah punya akun? <a href=\"{{ link }}\">Login</a> untuk checkout lebih cepat.", "see_items": {"one": "Lihat {{ count }} item", "other": "Lihat {{ count }} item"}, "view_all": "<PERSON><PERSON>a", "add": "Tambah", "choose": "<PERSON><PERSON><PERSON>", "added": "Ditambahkan", "show_less": "Semb<PERSON><PERSON><PERSON> la<PERSON>ya", "show_more": "Selengkapnya", "close": "<PERSON><PERSON><PERSON>", "more": "Selengkapnya", "zoom": "<PERSON><PERSON><PERSON>", "close_dialog": "Tutup dialog", "reset": "Reset", "remove_discount": "Hapus diskon {{ code }}", "enter_using_password": "<PERSON><PERSON><PERSON> den<PERSON> sandi", "submit": "<PERSON><PERSON>", "enter_password": "<PERSON><PERSON><PERSON><PERSON> sandi", "view_store_information": "<PERSON><PERSON> informasi toko", "back": "Kembali", "log_in": "<PERSON><PERSON><PERSON>", "log_out": "Logout", "apply": "<PERSON><PERSON>", "sign_in_options": "Opsi masuk la<PERSON>ya", "open_image_in_full_screen": "<PERSON><PERSON> gambar dalam layar penuh", "sign_up": "<PERSON><PERSON><PERSON>", "sort": "Urut<PERSON>", "show_all_options": "<PERSON><PERSON><PERSON><PERSON> semua opsi"}, "content": {"reviews": "<PERSON><PERSON><PERSON>", "language": "Bahasa", "localization_region_and_language": "<PERSON>ila<PERSON> dan bahasa", "no_results_found": "<PERSON><PERSON> t<PERSON>", "cart_total": "Total keranjang", "your_cart_is_empty": "Keranjang Anda kosong", "product_image": "Gambar produk", "product_information": "Informasi produk", "quantity": "<PERSON><PERSON><PERSON>", "product_total": "Total produk", "cart_estimated_total": "Estimasi total", "seller_note": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "cart_subtotal": "Subtotal", "discounts": "Diskon", "discount": "Diskon", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Termasuk bea cukai dan pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "Termasuk bea cukai dan pajak. Diskon dan biaya pengiriman dihitung saat checkout.", "taxes_included_shipping_at_checkout_with_policy_html": "Termasuk pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.", "taxes_included_shipping_at_checkout_without_policy": "Termasuk pajak. Diskon dan biaya pengiriman dihitung saat checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Termasuk bea cukai. <PERSON><PERSON>, diskon, dan <a href=\"{{ link }}\">biaya pen<PERSON></a> dihitung saat checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Termasuk bea cukai. <PERSON><PERSON>, diskon, dan biaya pengiriman dihitung saat checkout.", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON>, diskon, dan <a href=\"{{ link }}\">biaya <PERSON></a> dihitung saat checkout.", "taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON>, diskon, dan biaya pengiriman dihitung saat checkout.", "checkout": "Check out", "cart_title": "Keranjang", "price": "<PERSON><PERSON>", "price_regular": "<PERSON><PERSON> reguler", "price_compare_at": "<PERSON><PERSON><PERSON> dengan harga", "price_sale": "Harga obral", "duties_and_taxes_included": "Termasuk bea cukai dan pajak.", "duties_included": "Termasuk bea cukai.", "shipping_policy_html": "<a href=\"{{ link }}\"><PERSON><PERSON><PERSON></a> dihitung saat checkout.", "taxes_included": "Termasuk pajak.", "product_badge_sold_out": "Habis", "product_badge_sale": "Promo", "search_input_label": "<PERSON><PERSON>", "search_input_placeholder": "<PERSON><PERSON>", "search_results": "<PERSON><PERSON>", "search_results_label": "<PERSON><PERSON>", "search_results_no_results": "Tidak ada hasil yang ditemukan untuk \"{{ terms }}\". Coba pencarian lain.", "search_results_resource_articles": "Postingan blog", "search_results_resource_collections": "<PERSON><PERSON><PERSON><PERSON>", "search_results_resource_pages": "<PERSON><PERSON>", "search_results_resource_products": "Produk", "search_results_resource_queries": "<PERSON><PERSON>ian", "search_results_view_all": "<PERSON><PERSON>a", "search_results_view_all_button": "<PERSON><PERSON>a", "search_results_resource_products_count": {"one": "{{ count }} produk", "other": "{{ count }} produk"}, "grid_view": {"default_view": "<PERSON><PERSON><PERSON>", "grid_fieldset": "<PERSON><PERSON> kolom", "single_item": "<PERSON><PERSON><PERSON>", "zoom_out": "<PERSON><PERSON><PERSON><PERSON>"}, "recently_viewed_products": "<PERSON>u saja dilihat", "collection_placeholder": "<PERSON><PERSON><PERSON>", "product_card_placeholder": "<PERSON><PERSON><PERSON> produk", "unavailable": "Tidak tersedia", "product_count": "<PERSON><PERSON><PERSON> produk", "item_count": {"one": "{{ count }} item", "other": "{{ count }} item"}, "errors": "<PERSON><PERSON><PERSON>", "price_from": "<PERSON><PERSON> {{ price }}", "search": "<PERSON><PERSON>", "search_results_no_results_check_spelling": "Tidak ada hasil yang ditemukan untuk \"{{ terms }}\". <PERSON><PERSON><PERSON> ejaan atau gunakan kata atau frasa yang berbeda.", "featured_products": "<PERSON><PERSON><PERSON>", "no_products_found": "Tidak ada produk yang di<PERSON>n.", "use_fewer_filters_html": "Coba kurangi filter, atau <a class=\"{{ class }}\" href=\"{{ link }}\">hapus semua filter</a>.", "filters": "Filter", "price_filter_html": "<PERSON>rga terting<PERSON>ya adalah {{ price }}", "blog_details_separator": "|", "read_more": "Baca selengkapnya...", "discount_code": "<PERSON><PERSON> diskon", "pickup_available_at_html": "Pengambilan dapat di<PERSON>ukan di <b>{{ location }}</b>", "pickup_available_in": "<PERSON><PERSON><PERSON><PERSON> dapat dilakukan pada {{ pickup_time }}", "pickup_not_available": "Pengambilan saat ini tidak tersedia", "pickup_ready_in": "{{ pickup_time }}", "wrong_password": "<PERSON><PERSON>", "account_title": "<PERSON><PERSON><PERSON>", "account_title_personalized": "Hai {{ first_name }}", "account_orders": "<PERSON><PERSON><PERSON>", "account_profile": "Profil", "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk bea cukai dan pajak. Biaya pengiriman dihitung saat checkout.", "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "Termasuk bea cukai dan pajak. Biaya pengiriman dihitung saat checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk bea cukai. Biaya pengiriman dihitung saat checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Termasuk bea cukai. Biaya pengiriman dihitung saat checkout.", "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "<PERSON><PERSON> dan <a href=\"{{ link }}\">biaya <PERSON></a> dihitung saat checkout.", "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "<PERSON>jak dan biaya pengiriman dihitung saat checkout.", "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk pajak. Biaya pengiriman dihitung saat checkout.", "taxes_included_shipping_at_checkout_without_policy_without_discounts": "Termasuk pajak. Biaya pengiriman dihitung saat checkout.", "view_more_details": "Lihat <PERSON> lebih lan<PERSON>t", "inventory_low_stock": "Stok sedikit", "inventory_in_stock": "Tersedia", "inventory_out_of_stock": "Stok habis", "page_placeholder_title": "<PERSON><PERSON><PERSON>", "page_placeholder_content": "<PERSON><PERSON><PERSON> halaman untuk menampilkan kontennya.", "placeholder_image": "Gambar placeholder", "inventory_low_stock_show_count": {"one": "<PERSON><PERSON><PERSON> {{ count }}", "other": "<PERSON><PERSON><PERSON> {{ count }}"}, "discount_code_error": "Kode diskon tidak dapat diterapkan ke keranjang Anda", "shipping_policy": "Biaya pengiriman dihitung saat checkout.", "shipping_discount_error": "Diskon biaya pengiriman akan ditampilkan saat checkout setelah menambah<PERSON> alamat", "powered_by": "Toko ini didukung oleh", "store_owner_link_html": "Anda pemilik toko? <a href=\"{{ link }}\"><PERSON><PERSON> di sini</a>", "recipient_form_send_to": "<PERSON><PERSON> ke", "recipient_form_email_label": "<PERSON><PERSON>", "recipient_form_email_label_my_email": "<PERSON><PERSON> saya", "recipient_form_email_label_optional_for_no_js_behavior": "<PERSON><PERSON> (opsional)", "recipient_form_email_address": "Alamat email penerima", "recipient_form_name_label": "<PERSON><PERSON> (opsional)", "recipient_form_message_label": "<PERSON><PERSON> (opsional)", "recipient_form_message": "<PERSON><PERSON> (opsional)", "recipient_form_characters_used": "{{ used_chars }}/{{ max_chars }} karakter digunakan", "recipient_form_send_on": "TTTT-BB-HH", "recipient_form_send_on_label": "<PERSON><PERSON> (opsional)"}, "gift_cards": {"issued": {"how_to_use_gift_card": "Gunakan kode voucher secara online atau kode QR di toko", "title": "Ini dia voucher senilai {{ value }} Anda untuk {{ shop }}!", "subtext": "Voucher <PERSON>a", "shop_link": "Kunjungi toko online", "add_to_apple_wallet": "Tambahkan ke Apple Wallet", "qr_image_alt": "Kode QR — pindai untuk menukarkan voucher", "copy_code": "<PERSON>in kode voucher", "expiration_date": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada {{ expires_on }}", "copy_code_success": "<PERSON><PERSON> ber<PERSON><PERSON> disalin", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "placeholders": {"password": "<PERSON><PERSON>", "search": "<PERSON><PERSON>", "product_title": "<PERSON><PERSON><PERSON> produk", "collection_title": "<PERSON><PERSON><PERSON>"}, "products": {"product": {"add_to_cart": "Tambahkan ke keranjang", "added_to_cart": "Ditambahkan ke keranjang", "adding_to_cart": "Menambahkan...", "add_to_cart_error": "Kesalahan saat menambahkan ke keranjang", "sold_out": "Habis", "unavailable": "Tidak Tersedia"}}, "fields": {"separator": "hingga"}, "blogs": {"article": {"comment_author_separator": "•", "comments_heading": {"one": "{{ count }} komentar", "other": "{{ count }} komentar"}}, "comment_form": {"email": "Email", "error": "<PERSON><PERSON><PERSON> gagal di<PERSON>, per<PERSON><PERSON> hal-hal berikut:", "heading": "<PERSON><PERSON> k<PERSON>", "message": "<PERSON><PERSON>", "moderated": "Ingat, komentar perlu disetujui sebelum dipublikasikan.", "name": "<PERSON><PERSON>", "post": "Posting komentar", "success_moderated": "<PERSON><PERSON><PERSON> diposting, menunggu moderasi", "success": "Komentar diposting"}}, "pagefly": {"products": {"product": {"regular_price": "Regular price", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to cart", "back_to_collection": "Back to {{ title }}", "view_details": "View details"}}, "article": {"tags": "Tags:", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}, "password_page": {"login_form_message": "Enter store using password:", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "password_link": "Enter using password"}}}