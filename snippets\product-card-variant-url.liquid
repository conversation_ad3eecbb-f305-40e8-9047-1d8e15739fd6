{%- doc -%}
  Logic to determine which variant URL to use (matching swatch selection logic)

  @param {object} product - The product object

  @example
  {% render 'product-card-variant-url', product: product %}
{%- enddoc -%}

{% liquid
  assign variant_to_link = product.selected_or_first_available_variant
  assign combined_listing_count = product.options_with_values | map: 'values' | map: 'product_url' | compact | size

  # For now if it's combined listing, we don't need to do anything.
  unless combined_listing_count > 0
    # Simple direct check: which variant owns the featured image?
    if product.featured_media
      for variant in product.variants
        if variant.featured_media.id == product.featured_media.id
          assign variant_to_link = variant
          break
        endif
      endfor
    endif
  endunless
%}
{{- variant_to_link.url -}}
