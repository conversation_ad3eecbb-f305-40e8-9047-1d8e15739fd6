<script type="importmap">
  {
    "imports": {
      "@theme/critical": "{{ 'critical.js' | asset_url }}",
      "@theme/product-title": "{{ 'product-title-truncation.js' | asset_url }}",
      "@theme/component": "{{ 'component.js' | asset_url }}",
      "@theme/dialog": "{{ 'dialog.js' | asset_url }}",
      "@theme/events": "{{ 'events.js' | asset_url }}",
      "@theme/focus": "{{ 'focus.js' | asset_url }}",
      "@theme/morph": "{{ 'morph.js' | asset_url }}",
      "@theme/paginated-list": "{{ 'paginated-list.js' | asset_url }}",
      "@theme/performance": "{{ 'performance.js' | asset_url }}",
      "@theme/product-form": "{{ 'product-form.js' | asset_url }}",
      "@theme/recently-viewed-products": "{{ 'recently-viewed-products.js' | asset_url }}",
      "@theme/scrolling": "{{ 'scrolling.js' | asset_url }}",
      "@theme/section-renderer": "{{ 'section-renderer.js' | asset_url }}",
      "@theme/utilities": "{{ 'utilities.js' | asset_url }}",
      "@theme/variant-picker": "{{ 'variant-picker.js' | asset_url }}",
      "@theme/media-gallery": "{{ 'media-gallery.js' | asset_url }}",
      "@theme/quick-add": "{{ 'quick-add.js' | asset_url }}",
      "@theme/paginated-list-aspect-ratio": "{{ 'paginated-list-aspect-ratio.js' | asset_url }}",
      "@theme/section-animations": "{{ 'section-animations.js' | asset_url }}"
    }
  }
</script>

{% if settings.transition_to_main_product %}
  {% # theme-check-disable ParserBlockingScript %}
  <script
    src="{{ 'view-transitions.js' | asset_url }}"
    async
    blocking="render"
  ></script>
  {% # theme-check-enable %}
{% endif %}

<link
  rel="modulepreload"
  href="{{ 'critical.js' | asset_url }}"
>

<link
  rel="modulepreload"
  href="{{ 'utilities.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'component.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'section-renderer.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'morph.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'section-animations.js' | asset_url }}"
  fetchpriority="low"
>

{% if template.name == 'collection' or template.name == 'search' %}
  <link
    rel="modulepreload"
    href="{{ 'paginated-list-aspect-ratio.js' | asset_url }}"
    fetchpriority="low"
  >
  <link
    rel="modulepreload"
    href="{{ 'paginated-list.js' | asset_url }}"
    fetchpriority="low"
  >

  <link
    rel="modulepreload"
    href="{{ 'product-title-truncation.js' | asset_url }}"
    fetchpriority="low"
  >
{% endif %}

<link
  rel="modulepreload"
  href="{{ 'focus.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'recently-viewed-products.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'scrolling.js' | asset_url }}"
  fetchpriority="low"
>
<link
  rel="modulepreload"
  href="{{ 'events.js' | asset_url }}"
  fetchpriority="low"
>
<script
  src="{{ 'quick-add.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'section-animations.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
{% if settings.show_add_discount_code %}
  <script
    src="{{ 'cart-discount.js' | asset_url }}"
    type="module"
    fetchpriority="low"
  ></script>
{% endif %}
<script
  src="{{ 'dialog.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'variant-picker.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'product-card.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'product-form.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'accordion-custom.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'media.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'product-price.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'product-title-truncation.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'product-inventory.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'show-more.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'slideshow.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'anchored-popover.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'floating-panel.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'video-background.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'component-quantity-selector.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'media-gallery.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'rte-formatter.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>
<script
  src="{{ 'gift-card-recipient-form.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>

{% if localization.available_countries.size > 1 or localization.available_languages.size > 1 %}
  <script
    src="{{ 'localization.js' | asset_url }}"
    type="module"
    fetchpriority="low"
  ></script>
{% endif %}

{% if template == 'product' %}
  <script type="module">
    import { RecentlyViewed } from '@theme/recently-viewed-products';
    RecentlyViewed.addProduct('{{ product.id }}');
  </script>
{% endif %}

{% if settings.transition_to_main_product %}
  <script
    src="{{ 'product-card-link.js' | asset_url }}"
    type="module"
    fetchpriority="low"
  ></script>
{% endif %}

<script
  src="{{ 'auto-close-details.js' | asset_url }}"
  defer="defer"
></script>

<script>
  const basePath = 'https://cdn.shopify.com/static/themes/horizon/placeholders';
  const Theme = {
    placeholders: {
      general: [
        `${basePath}/general-1.png`,
        `${basePath}/general-2.png`,
        `${basePath}/general-3.png`,
        `${basePath}/general-4.png`,
        `${basePath}/general-5.png`,
        `${basePath}/general-6.png`,
        `${basePath}/general-7.png`,
      ],
      product: [`${basePath}/product-ball.png`, `${basePath}/product-cone.png`, `${basePath}/product-cube.png`],
    },
    translations: {
      placeholder_image: `{{ 'content.placeholder_image' | t }}`,
      added: `{{ 'actions.added' | t }}`,
      recipient_form_fields_visible: `{{ 'content.recipient_form_fields_visible' | t }}`,
      recipient_form_fields_hidden: `{{ 'content.recipient_form_fields_hidden' | t }}`,
      recipient_form_error: `{{ 'content.recipient_form_error' | t }}`,
    },
    routes: {
      cart_add_url: '{{ routes.cart_add_url | append: '.js' }}',
      cart_change_url: '{{ routes.cart_change_url }}',
      cart_update_url: '{{ routes.cart_update_url }}',
      cart_url: '{{ routes.cart_url }}',
      predictive_search_url: '{{ routes.predictive_search_url }}',
      search_url: '{{ routes.search_url }}',
    },
    template: {
      name: '{{ template }}',
    },
  };
</script>
