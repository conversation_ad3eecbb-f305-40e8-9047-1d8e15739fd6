

<script
  src="{{ 'results-list.js' | asset_url }}"
  type="module"
  fetchpriority="low"
></script>

{% javascript %}
  const url = new URL(window.location.href);
  if (url.hash) {
    document.addEventListener(
      'DOMContentLoaded',
      () => {
        const card = document.getElementById(url.hash.slice(1));
        if (card) {
          card.scrollIntoView({ behavior: 'instant' });
        }
      },
      { once: true }
    );
  }
{% endjavascript %}

{% comment %} We always render this full-width, as the child blocks have width: page/full settings {% endcomment %}
<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<results-list
  class="section product-grid-container color-{{ section.settings.color_scheme }}"
  style="--padding-block-start: {{ section.settings.padding-block-start }}px; --padding-block-end: {{ section.settings.padding-block-end }}px;"
  section-id="{{ section.id }}"
>
  {% render 'skip-to-content-link', href: '#ResultsList', text: 'accessibility.skip_to_results_list' %}

  <div
    class="collection-wrapper grid gap-style"
  >
    {% content_for 'block',
      type: 'filters',
      id: 'filters',
      results: collection,
      results_size: collection.products_count
    %}

    {% paginate collection.products by 24 %}
      {% capture children %}
        {% for product in collection.products %}
          <li
            id="{{ section.id }}-{{ product.id }}"
            class="product-grid__item product-grid__item--{{ forloop.index0 }}"
            data-page="{{ paginate.current_page }}"
            data-product-id="{{ product.id }}"
            ref="cards[]"
          >
            {% # theme-check-disable %}
            {% content_for 'block', type: '_product-card', id: 'product-card', closest.product: product %}
            {% # theme-check-enable %}
          </li>
        {% endfor %}
      {% endcapture %}

      {% render 'product-grid',
        section: section,
        children: children,
        products: collection.products,
        paginate: paginate
      %}
    {% endpaginate %}
  </div>
</results-list>

{% stylesheet %}
  .main-collection-grid {
    grid-column: var(--grid-column--mobile);

    @media screen and (min-width: 750px) {
      grid-column: var(--grid-column--desktop);
    }
  }

  .collection-wrapper {
    @media screen and (min-width: 750px) {
      grid-template-columns:
        1fr repeat(
          var(--centered-column-number),
          minmax(0, calc((var(--page-width) - var(--page-margin) * 2) / var(--centered-column-number)))
        )
        1fr;
    }
  }

  .collection-wrapper:has(.facets-block-wrapper--full-width),
  .collection-wrapper:has(.collection-wrapper--full-width) {
    @media screen and (min-width: 750px) {
      grid-column: 1 / -1;
      grid-template-columns:
        minmax(var(--page-margin), 1fr) repeat(
          var(--centered-column-number),
          minmax(0, calc((var(--page-width) - var(--page-margin) * 2) / var(--centered-column-number)))
        )
        minmax(var(--page-margin), 1fr);
    }
  }

  .collection-wrapper:has(.facets--vertical) .facets-block-wrapper--vertical:not(.hidden) ~ .main-collection-grid {
    @media screen and (min-width: 750px) {
      grid-column: var(--facets-vertical-col-width) / var(--full-width-column-number);
    }
  }

  .collection-wrapper:has(.facets-block-wrapper--vertical:not(#filters-drawer)):has(.collection-wrapper--full-width) {
    @media screen and (min-width: 750px) {
      grid-column: 1 / -1;
      grid-template-columns: 0fr repeat(var(--centered-column-number), minmax(0, 1fr)) 0fr;
    }
  }

  :is(.collection-wrapper--full-width, .collection-wrapper--full-width-on-mobile)
    [product-grid-view='default']
    .product-grid__card {
    @media screen and (max-width: 749px) {
      padding-inline-start: max(var(--padding-xs), var(--padding-inline-start));
      padding-inline-end: max(var(--padding-xs), var(--padding-inline-end));
    }
  }

  :is(.collection-wrapper--full-width, .collection-wrapper--full-width-on-mobile)
    [product-grid-view='mobile-single']
    .product-grid__card {
    @media screen and (max-width: 749px) {
      padding-inline-start: max(var(--padding-xs), var(--padding-inline-start));
      padding-inline-end: max(var(--padding-xs), var(--padding-inline-end));
    }
  }

  /* Make product media go edge-to-edge by using negative margins */
  :is(.collection-wrapper--full-width) .card-gallery,
  :is(.collection-wrapper--full-width-on-mobile) .card-gallery {
    @media screen and (max-width: 749px) {
      margin-inline-start: calc(-1 * max(var(--padding-xs), var(--padding-inline-start)));
      margin-inline-end: calc(-1 * max(var(--padding-xs), var(--padding-inline-end)));
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.collection_container",
  "enabled_on": {
    "templates": ["collection"]
  },
  "settings": [
    {
      "type": "select",
      "id": "layout_type",
      "label": "t:settings.type",
      "options": [
        {
          "value": "grid",
          "label": "t:options.grid"
        },
        {
          "value": "organic",
          "label": "t:options.editorial"
        }
      ],
      "default": "grid"
    },
    {
      "type": "select",
      "id": "product_card_size",
      "label": "t:settings.card_size",
      "options": [
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        },
        {
          "value": "extra-large",
          "label": "t:options.extra_large"
        }
      ],
      "default": "medium",
      "visible_if": "{{ section.settings.layout_type == 'grid' }}"
    },
    {
      "type": "select",
      "id": "mobile_product_card_size",
      "label": "t:settings.mobile_card_size",
      "options": [
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "large",
          "label": "t:options.large"
        }
      ],
      "default": "small"
    },
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "product_grid_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "centered",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "centered"
    },
    {
      "type": "checkbox",
      "id": "full_width_on_mobile",
      "label": "t:settings.full_width_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.product_grid_width != 'full-width' }}"
    },
    {
      "type": "range",
      "id": "columns_gap_horizontal",
      "label": "t:settings.horizontal_gap",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "columns_gap_vertical",
      "label": "t:settings.vertical_gap",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.section_layout"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8
    }
  ],
  "presets": []
}
{% endschema %}
