/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "cart-section": {
      "type": "main-cart",
      "blocks": {
        "cart-page-title": {
          "type": "_cart-title",
          "static": true,
          "settings": {
            "title": "Cart",
            "show_count": true,
            "type_preset": "h3",
            "alignment": "left",
            "padding-block-start": 16,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "cart-page-items": {
          "type": "_cart-products",
          "static": true,
          "settings": {
            "gap": 24,
            "image_ratio": "square",
            "dividers": true,
            "vendor": false,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "cart-page-summary": {
          "type": "_cart-summary",
          "static": true,
          "settings": {
            "extend_summary": false,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 4
          },
          "blocks": {}
        }
      },
      "settings": {
        "section_width": "page-width",
        "color_scheme": "",
        "padding-block-start": 48,
        "padding-block-end": 24
      }
    },
    "product_list_fUKeNV": {
      "type": "product-list",
      "blocks": {
        "static-header": {
          "type": "_product-list-content",
          "name": "t:names.header",
          "static": true,
          "settings": {
            "content_direction": "row",
            "vertical_on_mobile": false,
            "horizontal_alignment": "space-between",
            "vertical_alignment": "flex-end",
            "align_baseline": true,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_list_text_PyHYXr": {
              "type": "_product-list-text",
              "name": "t:names.collection_title",
              "settings": {
                "text": "<h3>You may also like...</h3>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "rte",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "product_list_button_76xpt8": {
              "type": "_product-list-button",
              "name": "t:names.product_list_button",
              "settings": {
                "label": "View all",
                "open_in_new_tab": false,
                "style_class": "link",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100
              },
              "blocks": {}
            }
          },
          "block_order": [
            "product_list_text_PyHYXr",
            "product_list_button_76xpt8"
          ]
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {
            "product_card_gap": 8,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_card_gallery_LxikbU": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media",
              "settings": {
                "image_ratio": "portrait",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group_YDRaEw": {
              "type": "_product-card-group",
              "name": "t:names.group",
              "settings": {
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 4,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "product_title_Ygnnee": {
                  "type": "product-title",
                  "name": "t:names.product_title",
                  "settings": {
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h5",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "var(--color-foreground)",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "price_tpntT4": {
                  "type": "price",
                  "name": "t:names.product_price",
                  "settings": {
                    "show_sale_price_first": true,
                    "show_installments": false,
                    "show_tax_info": false,
                    "type_preset": "",
                    "width": "100%",
                    "alignment": "left",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "color": "var(--color-foreground)",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "swatches_TfAq6M": {
                  "type": "swatches",
                  "name": "t:names.swatches",
                  "settings": {
                    "product_swatches_alignment": "flex-start",
                    "product_swatches_alignment_mobile": "flex-start",
                    "hide_padding": false,
                    "product_swatches_padding_top": 4,
                    "product_swatches_padding_bottom": 0,
                    "product_swatches_padding_left": 0,
                    "product_swatches_padding_right": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "product_title_Ygnnee",
                "price_tpntT4",
                "swatches_TfAq6M"
              ]
            }
          },
          "block_order": [
            "product_card_gallery_LxikbU",
            "group_YDRaEw"
          ]
        }
      },
      "name": "Featured collection",
      "settings": {
        "collection": "all",
        "layout_type": "carousel",
        "carousel_on_mobile": false,
        "max_products": 8,
        "columns": 4,
        "mobile_columns": "2",
        "mobile_card_size": "60cqw",
        "columns_gap": 8,
        "rows_gap": 36,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "horizontal_alignment": "flex-start",
        "gap": 28,
        "color_scheme": "scheme-1",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    }
  },
  "order": [
    "cart-section",
    "product_list_fUKeNV"
  ]
}