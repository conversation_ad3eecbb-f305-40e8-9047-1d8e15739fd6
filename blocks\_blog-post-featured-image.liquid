

{%- doc -%}
  Renders the blog post featured image block.

  @param {object} image - The image object from article.image
{%- enddoc -%}

{%- assign block_settings = block.settings -%}

<div
  class="blog-post-featured-image blog-post-featured-image--{{ block.id }} blog-post-featured-image--height-{{ block_settings.height }} spacing-style size-style"
  style="
    --ratio: {% case block_settings.image_ratio %}
      {% when 'landscape' %}16 / 9
      {% when 'portrait' %}4 / 5
      {% when 'adapt' %}{{ image.aspect_ratio | default: 1 }}
      {% else %}1
    {% endcase %};
    {% render 'size-style', settings: block_settings %}
    {% render 'spacing-style', settings: block_settings %}
  "
  {{ block.shopify_attributes }}
>
  {%- capture image_border_style -%}
    {% render 'border-override', settings: block_settings %}
  {%- endcapture -%}

  {{
    image
    | image_url: width: 3840
    | image_tag:
      width: image.width,
      widths: '240, 352, 832, 1200, 1600, 1920, 2560, 3840',
      height: image.height,
      class: 'blog-post-featured-image__image border-style',
      style: image_border_style,
      sizes: 'auto, (min-width: 750px) 100vw, 100vw',
      loading: 'eager',
      fetchpriority: 'high',
      alt: image.alt
  }}
</div>

{% stylesheet %}
  .blog-post-featured-image {
    --width: 100%;
    --custom-width: 100%;
    display: block;
    position: relative;
    width: var(--width);
  }

  .blog-post-featured-image.size-style {
    --width: var(--size-style-width, 100%);
  }

  .blog-post-featured-image--height-fit {
    height: fit-content;
  }

  .blog-post-featured-image--height-fill {
    height: 100%;
  }

  .blog-post-featured-image__image {
    aspect-ratio: var(--ratio);
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
  }

  @media screen and (max-width: 749px) {
    .blog-post-featured-image {
      --width: var(--width-mobile, var(--width));
      --custom-width: var(--custom-width-mobile, var(--custom-width));
    }

    .blog-post-featured-image.size-style {
      --width: var(--size-style-width-mobile, var(--size-style-width, 100%));
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.featured_image",
  "blocks": [],
  "settings": [
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "portrait",
          "label": "t:options.portrait"
        },
        {
          "value": "square",
          "label": "t:options.square"
        },
        {
          "value": "landscape",
          "label": "t:options.landscape"
        }
      ],
      "default": "adapt",
      "label": "t:settings.aspect_ratio"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width_desktop",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fill"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == 'custom' }}"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:settings.width_mobile",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fill"
    },
    {
      "type": "range",
      "id": "custom_width_mobile",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width_mobile == 'custom' }}"
    },
    {
      "type": "select",
      "id": "height",
      "label": "t:settings.height",
      "options": [
        {
          "value": "fit",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        }
      ],
      "default": "fit",
      "visible_if": "{{ block.settings.image_ratio == 'adapt' }}"
    },
    {
      "type": "header",
      "content": "t:content.borders"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.style",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "t:settings.thickness",
      "default": 1,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.opacity",
      "default": 100,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
