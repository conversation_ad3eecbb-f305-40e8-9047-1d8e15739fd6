{%- doc -%}
  Renders a background image

  @param {object} background_image - The background image
  @param {string} background_image_position - The background image position
  @param {string} section_id - The section ID
  @param {string} block_id - The block ID
  @param {number} [height] - The height of the background image
{%- enddoc -%}

<div
  class="background-image-container{% if background_image_position == 'fit' %} background-image-fit{% endif %}"
>
  {% liquid
    assign media_width_desktop = '100vw'
    assign media_width_mobile = '100vw'
    assign sizes = '(min-width: 750px) ' | append: media_width_desktop | append: ', ' | append: media_width_mobile
    assign widths = '240, 352, 832, 1200, 1600, 1920, 2560, 3840'
  %}

  {%- if background_image != blank -%}
    {% liquid
      assign fetch_priority = 'auto'
      if section and section.index == 1
        assign fetch_priority = 'high'
      endif
    %}
    {{
      background_image
      | image_url: width: 3840
      | image_tag: height: height, sizes: sizes, widths: widths, loading: 'eager', fetchpriority: fetch_priority
    }}
  {%- else -%}
    {%- assign placeholder_name = placeholder | default: 'hero-apparel-2' -%}
    {%- if placeholder_name == blank -%}
      {%- assign placeholder_name = 'hero-apparel-2' -%}
    {%- endif -%}
    {{ placeholder_name | placeholder_svg_tag }}
  {%- endif -%}
</div>
