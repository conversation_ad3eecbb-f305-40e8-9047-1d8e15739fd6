{%- doc -%}
  Renders a slideshow slide component.

  @param {number} index - the index of the slide
  @param {string} [children] - The content of the slideshow slide
  @param {string} [class] - HTML class attribute of the slideshow slide
  @param {string} [style] - HTML style attribute of the slideshow slide
  @param {string} [attributes] - Additional HTML attributes to add to the slideshow slide
  @param {boolean} [hidden] - Hidden slides will not be shown in the slideshow
  @param {string} [slide_id] - The unique id assigned to the slide amongst all slides in the slideshow
  @param {string} [media_fit] - { 'cover', 'contain' } - CSS property for how the media should be fit in the slide

  @example
  {% render 'slideshow-slide', index: 0, children: imageElement, slide_id: 'slide-1', hidden: false, media_fit: 'cover' %}
{%- enddoc -%}

{%- liquid
  assign class = class | strip | strip_newlines
  assign style = style | strip | strip_newlines
-%}

<slideshow-slide
  ref="slides[]"
  aria-hidden="{% if index == 0 %}false{% else %}true{% endif %}"
  style="view-timeline-name: --slide-{{ index }}; --product-media-fit: {{ media_fit | default: 'cover' }};"
  {% if class != blank %}
    class="{{ class }}"
  {% endif %}
  {{ attributes }}
  {% if style != blank %}
    style="{{ style }}"
  {% endif %}
  {% if slide_id != blank %}
    slide-id="{{ slide_id }}"
  {% endif %}
  {% if hidden == true %}
    hidden
  {% endif %}
  {{ attributes }}
>
  {{ children }}
</slideshow-slide>
