{%- comment -%}
  Intended for blocks and sections that provide values for all the referenced settings.

  <div class="spacing-style" style="{%  render 'spacing-style', settings: section.settings %}">

   Accepts:
      settings: {settings || section.settings}
{%- endcomment -%}

{% assign preset = preset | default: settings.type_preset %}

{%- capture variables -%}
  {%- if preset != 'rte' and settings.color != "" -%}
    --color: {{ settings.color }};
  {%- endif -%}
  {%- if preset == 'custom' -%}
    {% liquid
      unless type
        comment
          When choosing to customize the font, picking a specific font size
          determines the type of text block.
        endcomment
        if settings.font_size != ''
          assign font_size_value = settings.font_size | split: 'rem' | first | times: 1.0

          if font_size_value > 4.5
            assign type = 'display'
          elsif font_size_value > 3.5
            assign type = 'heading'
          else
            assign type = 'body'
          endif
        endif
      endunless
    %}
    {%- if settings.font_size != blank -%}
      {%- liquid
        assign font_size_rem = settings.font_size | split: 'rem' | first | times: 1.0
        assign fluid_size_cutoff_rem = 3.0

        if font_size_rem >= fluid_size_cutoff_rem
          assign target_viewport = 1400
          assign vw_value = font_size_rem | times: 16 | divided_by: target_viewport | times: 100

          assign scale_factor = font_size_rem | divided_by: fluid_size_cutoff_rem
          assign base_min_rem = 3.0
          assign scaling_bonus_rem = scale_factor | minus: 1 | times: 0.25
          assign dynamic_min_rem = base_min_rem | plus: scaling_bonus_rem
        endif
      -%}
      {%- if font_size_rem >= fluid_size_cutoff_rem -%}
        --font-size: clamp({{ dynamic_min_rem }}rem, {{ vw_value }}vw, {{ settings.font_size }});
      {%- else -%}
        --font-size: {{ settings.font_size }};
      {%- endif -%}
    {%- endif -%}
    {%- if settings.weight != blank -%}
      --font-weight: {{ settings.weight }};
    {% else %}
      --font-weight: {{ settings.font | replace: 'family', 'weight' }};
    {%- endif -%}
    --font-family: {{ settings.font }};
    --text-transform: {{ settings.case }};
    --text-wrap: {{ settings.wrap }};
    {% if settings.type_preset == 'custom' and settings.font_size == blank %}
      --line-height--display: var(--line-height--display-{{ settings.line_height }});
      --line-height--heading: var(--line-height--heading-{{ settings.line_height }});
      --line-height--body: var(--line-height--body-{{ settings.line_height }});
    {% else %}
      --line-height: var(--line-height--{{ type }}-{{ settings.line_height }});
    {% endif %}
    --letter-spacing: var(--letter-spacing--{{ type }}-{{ settings.letter_spacing }});
  {%- endif -%}
{%- endcapture -%}
{{- variables | strip | strip_newlines -}}
