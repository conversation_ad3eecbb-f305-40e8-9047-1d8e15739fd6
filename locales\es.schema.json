{"names": {"404": "404", "borders": "<PERSON><PERSON>", "collapsible_row": "Fila desplegable", "colors": "Colores", "custom_section": "Sección personalizada", "icon": "Ícono", "logo_and_favicon": "Logo y favicon", "overlapping_blocks": "Bloques solapados", "product_buy_buttons": "Botones de compra", "product_description": "Descripción", "product_price": "Precio", "product_variant_picker": "Selector de variante", "slideshow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typography": "Tipografía", "video": "Video", "slideshow_controls": "Controles de la presentación de diapositivas", "size": "<PERSON><PERSON>", "spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_recommendations": "Productos recomendados", "product_media": "Elementos multimedia del producto", "featured_collection": "Colección destacada", "add_to_cart": "Agregar al carrito", "email_signup": "Suscriptor de correo electrónico", "submit_button": "Botón de enviar", "grid_layout_selector": "Selector de grid layout", "image": "Imagen", "list_items": "Artículos de la lista", "facets": "Facetas", "variants": "<PERSON><PERSON><PERSON>", "styles": "Estilos", "product_cards": "Tarjetas de producto", "primary_button": "Botón principal", "secondary_button": "Botón secundario", "popovers": "Elementos emergentes", "buttons": "Botones", "inputs": "Entradas", "marquee": "Marquesina", "alternating_content_rows": "Filas alternantes", "product_list": "Colección destacada", "spacer": "Separador", "pull_quote": "Cita destacada", "contact_form": "Formulario de contacto", "featured_product": "Aspecto destacado del producto", "icons_with_text": "Ícono con texto", "accelerated_checkout": "Proceso de pago acelerado", "accordion": "Acordeón", "accordion_row": "Fila del acordeón", "animations": "Animaciones", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "Barra de anuncios", "badges": "<PERSON><PERSON><PERSON>", "button": "Botón", "cart": "<PERSON><PERSON>", "cart_items": "Artículos en el carrito", "cart_products": "Productos en el carrito", "cart_title": "<PERSON><PERSON>", "collection": "Colección", "collection_card": "Tarjeta de la colección", "collection_columns": "Columnas de la colección", "collection_container": "Colección", "collection_description": "Descripción de la colección", "collection_image": "Imagen de la colección", "collection_info": "Información de la colección", "collection_list": "Lista de colecciones", "collections": "Colecciones", "content": "Contenido", "content_grid": "Contenido de la cuadrícula", "details": "Información", "divider": "Divisor", "filters": "Filtrado y ordenado", "follow_on_shop": "<PERSON><PERSON><PERSON> en <PERSON>", "footer": "Pie de página", "footer_utilities": "Utilidades del pie de página", "group": "Grupo", "header": "Encabezado", "heading": "Encabezado", "icons": "Íconos", "image_with_text": "Imagen con texto", "input": "Información", "logo": "Logo", "magazine_grid": "Cuadrícula de revista", "media": "Elementos multimedia", "menu": "Menú", "mobile_layout": "Diseño mó<PERSON>", "payment_icons": "Íconos de pago", "popup_link": "Enlace emergente", "predictive_search": "Ventana emergente de búsqueda", "predictive_search_empty": "Búsqueda predictiva vacía", "price": "Precio", "product": "Producto", "product_card": "Tarjeta del producto", "product_card_media": "Elementos multimedia", "product_card_rendering": "Renderizado de la tarjeta del producto", "product_grid": "Cuadrícula", "product_grid_main": "Cuadrícula del producto", "product_image": "Imagen del producto", "product_information": "Información del producto", "product_review_stars": "Revisar las estrellas", "quantity": "Cantidad", "row": "<PERSON><PERSON>", "search": "Buscar", "section": "Sección", "selected_variants": "<PERSON><PERSON><PERSON> se<PERSON>", "shop_the_look": "Comprar el look", "slide": "Diapositiva", "social_media_links": "Enlaces de redes sociales", "steps": "Pasos", "summary": "Resumen", "swatches": "Muestras", "testimonials": "Testimonios", "text": "Texto", "title": "<PERSON><PERSON><PERSON><PERSON>", "utilities": "Utilidades", "search_input": "Buscar entrada", "search_results": "Resultados de la búsqueda", "read_only": "Solo lectura", "collections_bento": "Lista de colecciones: <PERSON><PERSON>", "faq_section": "Preguntas frecuentes", "hero": "Hero", "jumbo_text": "Texto de Jumbo", "video_section": "Video", "custom_liquid": "Liquid personalizado", "blog": "Blog", "blog_post": "Artí<PERSON>lo del blog", "blog_posts": "Artículos del blog", "caption": "Leyenda", "collection_card_image": "Imagen", "collection_title": "Tí<PERSON>lo de la colección", "collection_links": "Enlaces de la colección", "collection_links_spotlight": "Enlaces de colecciones: Spotlight", "collection_links_text": "Enlaces de colecciones: texto", "collections_carousel": "Lista de colecciones: carrusel", "collections_editorial": "Lista de colecciones: Editorial", "collections_grid": "Lista de colecciones: cuadrícula", "copyright": "Derechos de autor", "count": "Recuento", "divider_section": "Divisor", "drawers": "<PERSON><PERSON><PERSON>", "editorial": "Editorial", "editorial_jumbo_text": "Editorial: <PERSON><PERSON>", "hero_marquee": "Hero: ma<PERSON><PERSON><PERSON>", "input_fields": "Campos de entrada", "local_pickup": "Retiro en tienda", "marquee_section": "Marquesina", "media_with_text": "Multimedia con texto", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_content": "Contenido", "page_layout": "Diseño de página", "policy_list": "Enlaces a la política", "prices": "<PERSON><PERSON><PERSON>", "products_carousel": "Colección destacada: Carrusel", "products_editorial": "Colección destacada: Editorial", "products_grid": "Colección destacada: Cuadrícula", "social_link": "Enlace a redes sociales", "split_showcase": "Dividir presentac<PERSON>", "variant_pickers": "Selectores de variante", "view_all_button": "Ver todo", "product_title": "Nombre del producto", "large_logo": "Logo grande", "product_list_button": "Bot<PERSON> Ver todo", "product_inventory": "Inventario de productos", "pills": "Cáps<PERSON><PERSON>", "description": "Descripción", "featured_image": "Imagen destacada"}, "settings": {"alignment": "Alineación", "autoplay": "Reproducción automática", "background": "Fondo", "border_radius": "Radio de esquina", "border_width": "Grosor del borde", "borders": "<PERSON><PERSON>", "bottom_padding": "Relleno inferior", "button": "Botón", "color": "Color", "colors": "Colores", "content_alignment": "Alineación de contenido", "content_direction": "Dirección del contenido", "content_position": "Posición del contenido", "cover_image_size": "Tamaño de la imagen de portada", "cover_image": "Imagen de portada", "custom_minimum_height": "Altura mínima personalizada", "custom_width": "<PERSON><PERSON>", "enable_video_looping": "Reproducción de video en bucle", "favicon": "Favicon", "font_family": "Familia de fuentes", "gap": "Espacio", "geometric_translate_y": "Traslación Y geométrica", "heading": "Encabezado", "icon": "Ícono", "image": "Imagen", "image_icon": "Ícono de imagen", "image_opacity": "Opacidad de la imagen", "image_position": "Posición de la imagen", "image_ratio": "Relación de aspecto de imagen", "label": "Etiqueta", "line_height": "Altura de línea", "link": "Enlace", "layout_gap": "Espacio de diseño", "make_section_full_width": "Definir ancho completo en la sección", "minimum_height": "<PERSON><PERSON> mínima", "opacity": "Opacidad", "overlay_opacity": "Opacidad superpuesta", "padding": "<PERSON><PERSON><PERSON>", "primary_color": "Enlaces", "product": "Producto", "section_width": "<PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "slide_spacing": "Espacio entre diapositivas", "slide_width": "<PERSON><PERSON> diapositiva", "slideshow_fullwidth": "Diapositivas de ancho completo", "style": "<PERSON><PERSON><PERSON>", "text": "Texto", "text_case": "Caso", "top_padding": "Relleno superior", "video": "Video", "video_alt_text": "Texto alternativo", "video_loop": "Video en bucle", "video_position": "Posición del video", "width": "<PERSON><PERSON><PERSON>", "z_index": "<PERSON><PERSON><PERSON> Z", "limit_content_width": "Ancho lí<PERSON> del contenido", "color_scheme": "Esquema de colores", "inherit_color_scheme": "Heredar esquema de colores", "product_count": "Conteo de productos", "product_type": "Tipo de producto", "content_width": "Ancho del contenido", "collection": "Colección", "enable_sticky_content": "Contenido fijo en el escritorio", "error_color": "Error", "success_color": "Correcto", "primary_font": "<PERSON>ente principal", "secondary_font": "Fuente secundaria", "tertiary_font": "Fuente terciaria", "columns": "Columnas", "items_to_show": "Artículos que mostrar", "layout": "Diseño", "layout_type": "Tipo", "show_grid_layout_selector": "Mostrar el selector de grid layout", "view_more_show": "Mostrar el botón Ver más", "image_gap": "Separación entre imágenes", "width_desktop": "Anchura del escritorio", "width_mobile": "<PERSON><PERSON><PERSON>", "border_style": "<PERSON><PERSON><PERSON>", "height": "Altura", "thickness": "Grosor", "stroke": "Trazo", "filter_style": "Filtrar por estilo", "swatches": "Muestras", "quick_add_colors": "Colores de agregado rápido", "divider_color": "Divisor", "border_opacity": "Opacidad del borde", "hover_background": "Color del fondo al pasar el cursor", "hover_borders": "Color de los bordes al pasar el cursor", "hover_text": "Color del texto al pasar el cursor", "primary_hover_color": "Color de los enlaces al pasar el cursor", "primary_button_text": "Texto del botón principal", "primary_button_background": "Fondo del botón principal", "primary_button_border": "<PERSON><PERSON> del botón principal", "secondary_button_text": "Texto del botón secundario", "secondary_button_background": "Fondo del botón secundario", "secondary_button_border": "Borde del botón secundario", "shadow_color": "Sombra", "background_color": "Color de fondo", "video_autoplay": "Reproducción automática", "video_cover_image": "Imagen de portada", "video_external_url": "URL", "video_source": "Fuente", "first_row_media_position": "Posición del elemento multimedia en primera fila", "hide_padding": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>o", "size_mobile": "Tamaño del móvil", "pixel_size_mobile": "Tamaño del píxel", "percent_size_mobile": "Tamaño del porcentaje", "unit": "Unidad", "custom_mobile_size": "Tamaño de móvil personalizado", "fixed_height": "Altura del píxel", "fixed_width": "Ancho <PERSON> píxel", "percent_height": "Altura del porcentaje", "percent_width": "<PERSON><PERSON> del porcentaje", "percent_size": "Tamaño del porcentaje", "pixel_size": "Tamaño del píxel", "accordion": "Acordeón", "aspect_ratio": "Relación de aspecto", "auto_rotate_announcements": "Rotar anuncios automáticamente", "auto_rotate_slides": "Rotar las diapositivas automáticamente", "badge_corner_radius": "Radio de esquina", "badge_position": "Posición en las tarjetas", "badge_sale_color_scheme": "<PERSON><PERSON><PERSON>", "badge_sold_out_color_scheme": "Agotado", "behavior": "Comportamiento", "blur": "Sombreado difuminado", "border": "<PERSON>rde", "bottom": "Abajo", "card_image_height": "Altura de la imagen del producto", "carousel_on_mobile": "Carrusel en móvil", "cart_count": "Conteo del carrito", "cart_items": "Artículos en el carrito", "cart_related_products": "Productos relacionados", "cart_title": "<PERSON><PERSON>", "cart_total": "Total del carrito", "cart_type": "Tipo", "case": "Caso", "checkout_buttons": "Botones de proceso de pago acelerado", "collection_list": "Colecciones", "collection_templates": "Plantilla de colección", "content": "Contenido", "corner_radius": "Radio de esquina", "country_region": "País o región", "currency_code": "Código <PERSON> moneda", "custom_height": "Altura personalizada", "desktop_height": "Altura del escritorio", "direction": "Dirección", "display": "<PERSON><PERSON><PERSON>", "divider_thickness": "Grosor del divisor", "divider": "Divisor", "dividers": "Divisores", "drop_shadow": "Sombra de Drop", "empty_state_collection_info": "Mostrar antes de ingresar la búsqueda", "empty_state_collection": "Colección en estado vacío", "enable_filtering": "<PERSON><PERSON><PERSON>", "enable_grid_density": "Control del diseño de la cuadrícula", "enable_sorting": "Ordenación", "enable_zoom": "Activar zoom", "equal_columns": "Columnas iguales", "expand_first_group": "Expandir el primer grupo", "extend_media_to_screen_edge": "Ampliar el elemento multimedia al borde de la pantalla", "extend_summary": "Ampliar al borde de la pantalla", "extra_large": "Extra grande", "extra_small": "Extra pequeño", "flag": "<PERSON><PERSON>", "font_price": "Fuente del precio", "font_weight": "Peso de la fuente", "font": "Fuente", "full_width_first_image": "Primera imagen de ancho completo", "full_width_on_mobile": "<PERSON><PERSON> completo de móvil", "heading_preset": "Encabezado predefinido", "hide_unselected_variant_media": "Ocultar elementos multimedia no seleccionados", "horizontal_gap": "Espacio horizontal", "horizontal_offset": "Sombreado de la desalineación horizontal", "hover_behavior": "Comportamiento al pasar el cursor", "icon_background": "Fondo del ícono", "icons": "Íconos", "image_border_radius": "Radio de la esquina de la imagen", "installments": "<PERSON><PERSON><PERSON>", "integrated_button": "Botón integrado", "language_selector": "Selector de idioma", "large": "Grande", "left_padding": "Relleno a la izquierda", "left": "Iz<PERSON>erda", "letter_spacing": "Espacia<PERSON>", "limit_media_to_screen_height": "Ajustar a la altura de la pantalla", "limit_product_details_width": "Limitar la anchura de los detalles del producto", "link_preset": "Enlace predefinido", "links": "Enlaces", "logo_font": "Fuente del logo", "logo": "Logo", "loop": "<PERSON><PERSON><PERSON>", "make_details_sticky_desktop": "Fijo en el escritorio", "max_width": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON>", "media_height": "Altura del elemento multimedia", "media_overlay": "Sobreposición del elemento multimedia", "media_position": "Posición del elemento multimedia", "media_type": "Tipo del elemento multimedia", "media_width": "Anchura del elemento multimedia", "menu": "Menú", "mobile_columns": "Columnas móviles", "mobile_height": "Altura móvil", "mobile_logo_image": "Logo en dispositivo móvil", "mobile_quick_add": "Agregado rápido móvil", "motion_direction": "Dirección de movimiento", "motion": "Movimiento", "movement_direction": "Dirección del movimiento", "navigation_bar_color_scheme": "Esquema de los colores de la barra de navegación", "navigation_bar": "Barra de navegación", "navigation": "Navegación", "open_new_tab": "<PERSON><PERSON>r el enlace en una pestaña nueva", "overlay_color": "Color de sobreposición", "overlay": "Superposición", "padding_bottom": "Relleno en la parte inferior", "padding_horizontal": "<PERSON><PERSON><PERSON> horizontal", "padding_top": "Relleno en la parte superior", "page_width": "<PERSON><PERSON>", "pagination": "Paginación", "placement": "Colocación", "position": "Posición", "preset": "Predefinido", "product_cards": "Tarjetas del producto", "product_pages": "Páginas de productos", "product_templates": "Plantilla de producto", "products": "Productos", "quick_add": "<PERSON><PERSON><PERSON><PERSON>", "ratio": "Proporción", "regular": "Normal", "review_count": "Conteo de la revisión", "right": "Derecha", "row_height": "Altura de la fila", "row": "<PERSON><PERSON>", "seller_note": "<PERSON><PERSON><PERSON> notas al vendedor", "shape": "Forma", "show_as_accordion": "Mostrar como acordeón en móvil", "show_sale_price_first": "Mostrar el precio de oferta primero", "show_tax_info": "Información fiscal", "show": "Mostrar", "small": "Pequeño", "speed": "Velocidad", "statement": "Extracto", "sticky_header": "Encabezado fijo", "text_hierarchy": "Jerarquía del texto", "text_presets": "Textos predefinidos", "title": "<PERSON><PERSON><PERSON><PERSON>", "top": "Arriba", "type": "Tipo", "type_preset": "Textos predefinidos", "underline_thickness": "Espesor de subrayado", "variant_images": "Imágenes de variantes", "vendor": "<PERSON><PERSON><PERSON><PERSON>", "vertical_gap": "Espacio vertical", "vertical_offset": "Sombreado de la desalineación vertical", "vertical_on_mobile": "Vertical en móvil", "view_all_as_last_card": "\"Ver todo\" como última tarjeta", "weight": "Peso", "wrap": "Ajustar", "read_only": "Solo lectura", "always_stack_buttons": "Apilar siempre los botones", "custom_mobile_width": "<PERSON>cho personalizado para móviles", "gradient_direction": "Dirección del degradado", "headings": "Encabezados", "overlay_style": "Estilo de sobreposición", "shadow_opacity": "Opacidad del sombreado", "show_filter_label": "Etiquetas de texto para los filtros aplicados", "show_swatch_label": "Etiquetas de texto para muestras", "transparent_background": "Fondo transparente", "account": "C<PERSON><PERSON>", "align_baseline": "Alinear la línea de base del texto", "animation_repeat": "Repetir animación", "add_discount_code": "Permit<PERSON> descuento<PERSON> en el carrito", "background_overlay": "Sobreposición del fondo", "background_media": "Elemento multimedia de fondo", "border_thickness": "Grosor del borde", "bottom_row": "Fila inferior", "button_text_case": "Caja de texto", "button_text_weight": "Grosor del texto", "auto_open_cart_drawer": "\"Agregar al carrito\" abre automáticamente el carrito lateral", "collection_count": "Conteo de colecciones", "custom_liquid": "Código de Liquid", "default": "Predeterminado", "default_logo": "Logo predeterminado", "divider_width": "Ancho del divisor", "effects": "Efectos", "hide_logo_on_home_page": "Ocultar logo en la página de inicio", "horizontal_padding": "<PERSON><PERSON><PERSON> horizontal", "inverse": "Inverso", "inverse_logo": "Logo inverso", "layout_style": "<PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON>", "mobile_pagination": "Paginación móvil", "open_row_by_default": "Abrir fila de forma predeterminada", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_transition_enabled": "Transición de la página", "search": "Búsqueda", "search_icon": "Ícono de búsqueda", "search_position": "Posición", "search_row": "<PERSON><PERSON>", "show_author": "Autor", "show_alignment": "Mostrar alineación", "show_count": "<PERSON>rar recuento", "show_date": "<PERSON><PERSON>", "show_pickup_availability": "Mostrar disponibilidad de retiro", "show_search": "<PERSON>rar b<PERSON>", "use_inverse_logo": "Utilizar logo inverso", "vertical_padding": "<PERSON><PERSON><PERSON> vertical", "visibility": "Visibilidad", "product_corner_radius": "Radio de la esquina del producto", "card_corner_radius": "Radio de la esquina de la tarjeta", "alignment_mobile": "Alineación para dispositivo móvil", "blurred_reflection": "Reflejo difuminado", "card_hover_effect": "Efecto hover de la tarjeta", "card_size": "Tamaño de tarjeta", "collection_title_case": "Caja del título de la colección", "inventory_threshold": "Umbral de existencias bajas", "mobile_card_size": "Tamaño de tarjeta en móvil", "product_and_card_title_case": "Caja del título del producto y la tarjeta", "product_title_case": "Caja del nombre del producto", "reflection_opacity": "Opacidad del reflejo", "right_padding": "<PERSON><PERSON><PERSON> derecho", "show_inventory_quantity": "Mostrar cantidad de existencias bajas", "text_label_case": "Caja de la etiqueta de texto", "transition_to_main_product": "Transición de la tarjeta de producto a la página de producto", "show_second_image_on_hover": "Mostrar segunda imagen al pasar el cursor", "media": "Elementos multimedia", "product_card_carousel": "<PERSON><PERSON> car<PERSON>", "media_fit": "Ajuste de elemento multimedia", "scroll_speed": "Desplazar<PERSON> al siguiente anuncio", "show_powered_by_shopify": "Mostrar \"Con tecnología de Shopify\"", "gift_card_form": "Formulario de tarjeta de regalo"}, "options": {"adapt_to_image": "Adaptar a la imagen", "apple": "Man<PERSON><PERSON>", "arrow": "Fle<PERSON>", "auto": "Automática", "banana": "<PERSON><PERSON><PERSON><PERSON>", "bottle": "Biberón", "box": "Caja", "buttons": "Botones", "carrot": "Zanahoria", "center": "Centrado", "chat_bubble": "Globo de chat", "clipboard": "Portapapeles", "contain": "<PERSON><PERSON><PERSON>", "counter": "<PERSON><PERSON><PERSON>", "cover": "Portada", "custom": "Personalizado", "dairy_free": "Sin lácteos", "dairy": "Lácteos", "default": "Predeterminada", "dropdowns": "Desplegables", "dots": "Punt<PERSON>", "dryer": "Secador", "end": "Fin", "eye": "<PERSON><PERSON>", "facebook": "Facebook", "fill": "<PERSON><PERSON><PERSON>", "fire": "Fuego", "fit": "<PERSON><PERSON><PERSON>", "full": "Completa", "full_and_page": "Fondo completo, contenido de ancho de página", "gluten_free": "Sin gluten", "heading": "Encabezado", "heart": "Corazón", "horizontal": "Horizontal", "instagram": "Instagram", "iron": "Plancha", "landscape": "<PERSON><PERSON><PERSON>", "large": "Grande", "leaf": "Hoja", "leather": "<PERSON><PERSON><PERSON>", "lg": "L", "lightning_bolt": "Relámpago", "link": "Enlace", "lipstick": "<PERSON><PERSON><PERSON><PERSON> labial", "lock": "Candado", "lowercase": "minúscula", "m": "M", "map_pin": "Alfiler en mapa", "medium": "Mediano", "none": "<PERSON><PERSON><PERSON>", "numbers": "Números", "nut_free": "Sin frutos secos", "outline": "Contorno", "page": "<PERSON><PERSON><PERSON><PERSON>", "pants": "<PERSON><PERSON><PERSON>", "paw_print": "<PERSON><PERSON> de una pata", "pepper": "Pimienta", "perfume": "Perfume", "pinterest": "Pinterest", "plane": "Avión", "plant": "Planta", "portrait": "Retrato", "price_tag": "Etiqueta de precio", "question_mark": "Signo de interrogación", "recycle": "Reciclar", "return": "Devolución", "ruler": "Regla", "s": "S", "sentence": "Oración", "serving_dish": "<PERSON> de <PERSON>rvir", "shirt": "<PERSON><PERSON>", "shoe": "Zapato", "silhouette": "<PERSON><PERSON><PERSON>", "small": "Pequeña", "snapchat": "Snapchat", "snowflake": "<PERSON><PERSON> de <PERSON>eve", "solid": "<PERSON><PERSON><PERSON><PERSON>", "space_between": "Espac<PERSON> entre", "square": "Cuadrado", "star": "Estrella", "start": "<PERSON><PERSON>o", "stopwatch": "Cronómetro", "tiktok": "TikTok", "truck": "Camión", "tumblr": "Tumblr", "twitter": "X (Twitter)", "uppercase": "<PERSON><PERSON><PERSON>", "vertical": "Vertical", "vimeo": "Vimeo", "washing": "<PERSON><PERSON><PERSON>", "circle": "<PERSON><PERSON><PERSON><PERSON>", "swatches": "Muestras", "full_and_page_offset_left": "Fondo completo, contenido de ancho de página, offset a la izquierda", "full_and_page_offset_right": "Fondo completo, contenido de ancho de página, offset a la derecha", "offset_left": "Offset a la izquierda", "offset_right": "Offset a la derecha", "page_center_aligned": "Página, alineado al centro", "page_left_aligned": "Página, alineado a la izquierda", "page_right_aligned": "Página, alineado a la derecha", "button": "Botón", "caption": "Leyenda", "h1": "Título 1", "h2": "Título 2", "h3": "Título 3", "h4": "Título 4", "h5": "Título 5", "h6": "Título 6", "paragraph": "<PERSON><PERSON><PERSON><PERSON>", "primary": "Principal", "secondary": "Secundario", "tertiary": "Terciario", "chevron_left": "Comillas angulares izquierda", "chevron_right": "Comillas angulares derecha", "diamond": "Diamante", "grid": "Cuadrícula", "parallelogram": "Paralelogramo", "rounded": "Redondeado", "fit_content": "Ajustar", "pills": "Cáps<PERSON><PERSON>", "heavy": "<PERSON><PERSON><PERSON>", "thin": "Fino", "drawer": "Cajón", "preview": "Vista previa", "text": "Texto", "up": "Arriba", "down": "Abajo", "gradient": "Degradado", "video_uploaded": "Subido", "video_external_url": "URL externa", "fixed": "<PERSON><PERSON>", "pixel": "Píxel", "percent": "Po<PERSON>entaj<PERSON>", "aspect_ratio": "Relación de aspecto", "above_carousel": "Arriba del carrusel", "all": "Todo", "always": "Siempre", "arrows_large": "Flechas grandes", "arrows": "<PERSON><PERSON><PERSON><PERSON>", "balance": "<PERSON><PERSON>", "bento": "<PERSON><PERSON>", "black": "Negro", "bluesky": "<PERSON><PERSON>", "body_large": "Cuerpo del texto (grande)", "body_regular": "<PERSON><PERSON><PERSON> del texto (regular)", "body_small": "Cuerpo del texto (pequeño)", "bold": "Negrita", "bottom_left": "Abajo a la izquierda", "bottom_right": "Abajo a la derecha", "bottom": "Abajo", "capitalize": "Capitalizar", "caret": "<PERSON><PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "check_box": "Casilla de verificación", "chevron_large": "Comillas angulares grandes", "chevron": "<PERSON><PERSON><PERSON>", "chevrons": "<PERSON><PERSON><PERSON>", "classic": "Clásico", "collection_images": "Imágenes de la colección", "color": "Color", "complementary": "Complementario", "dissolve": "<PERSON><PERSON><PERSON><PERSON>", "dotted": "Punteado", "editorial": "Editorial", "extra_large": "Extra grande", "extra_small": "Extra pequeño", "featured_collections": "Colecciones destacadas", "featured_products": "Productos destacados", "font_primary": "Principal", "font_secondary": "Secundario", "font_tertiary": "Terciario", "forward": "Adelante", "full_screen": "Pantalla completa", "heading_extra_large": "Encabezado (extra grande)", "heading_extra_small": "Encabezado (extra pequeño)", "heading_large": "Encabezado (grande)", "heading_regular": "Encabezado (normal)", "heading_small": "Encabezado (pequeño)", "icon": "Ícono", "image": "Imagen", "input": "Información", "inside_carousel": "<PERSON><PERSON> del carrus<PERSON>", "inverse_large": "Inverso grande", "inverse": "Inverso", "large_arrows": "Flechas grandes", "large_chevrons": "Comillas angulares grandes", "left": "Iz<PERSON>erda", "light": "<PERSON><PERSON><PERSON>", "linkedin": "LinkedIn", "loose": "Libre", "media_first": "Primer elemento multimedia", "media_second": "Segundo elemento multimedia", "modal": "Modal", "narrow": "Estrecho", "never": "Nunca", "next_to_carousel": "Junto al carrusel", "normal": "Normal", "nowrap": "Sin envolver", "off_media": "Elemento multimedia inactivado", "on_media": "Elemento multimedia activado", "on_scroll_up": "Al desplazarse hacia arriba", "one_half": "1/2", "one_number": "1", "one_third": "1/3", "pill": "Ovalado", "plus": "Plus", "pretty": "<PERSON><PERSON><PERSON><PERSON>", "price": "Precio", "primary_style": "Estilo principal", "rectangle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "regular": "Normal", "related": "Relacionado", "reverse": "Revertir", "rich_text": "Texto enriquecido", "right": "Derecha", "secondary_style": "Estilo secunda<PERSON>", "semibold": "Seminegrita", "shaded": "Sombreado", "show_second_image": "<PERSON>rar segunda imagen", "single": "Individual", "slide_left": "<PERSON><PERSON><PERSON> hacia la izquierda", "slide_up": "<PERSON><PERSON><PERSON> hacia arriba", "spotify": "Spotify", "stack": "<PERSON><PERSON><PERSON>", "text_only": "Solo texto", "threads": "<PERSON><PERSON>", "thumbnails": "Miniaturas", "tight": "Ajustado", "top_left": "Arriba a la izquierda", "top_right": "Arriba a la derecha", "top": "Arriba", "two_number": "2", "two_thirds": "2/3", "underline": "Subrayado", "video": "Video", "wide": "<PERSON><PERSON>", "youtube": "YouTube", "compact": "Compacto", "standard": "<PERSON><PERSON><PERSON><PERSON>", "accent": "<PERSON><PERSON><PERSON><PERSON>", "below_image": "<PERSON><PERSON> la <PERSON>n", "blur": "Difuminar", "body": "<PERSON><PERSON><PERSON>", "button_primary": "Botón principal", "button_secondary": "Botón secundario", "crop_to_fit": "Recortar para ajustar", "hidden": "Oculto", "hint": "Sugerencia", "maintain_aspect_ratio": "Mantener relación de aspecto", "off": "Desactivado", "on_image": "<PERSON><PERSON> la imagen", "reveal": "Mostrar", "social_bluesky": "Red social: <PERSON><PERSON>", "social_facebook": "Red social: Facebook", "social_instagram": "Red social: Instagram", "social_linkedin": "Red social: LinkedIn", "social_pinterest": "Red social: Pinterest", "social_snapchat": "Red social: <PERSON><PERSON><PERSON><PERSON>", "social_spotify": "Red social: Spotify", "social_threads": "Red social: <PERSON><PERSON>", "social_tiktok": "Red social: TikTok", "social_tumblr": "Red social: Tumblr", "social_twitter": "Red social: X (Twitter)", "social_whatsapp": "Red social: WhatsApp", "social_vimeo": "Red social: <PERSON><PERSON><PERSON>", "social_youtube": "Red social: YouTube", "spotlight": "Spotlight", "subheading": "Subtítulo", "lift": "Subir", "scale": "Escalar", "subtle_zoom": "Ampliar"}, "content": {"advanced": "<PERSON><PERSON><PERSON>", "background_image": "Imagen de fondo", "background_video": "Video de fondo", "block_size": "Tamaño de bloque", "borders": "<PERSON><PERSON>", "describe_the_video_for": "Describe el video para los clientes que usan lectores de pantalla. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "section_size": "Tamaño de sección", "slideshow_width": "<PERSON><PERSON> diapositiva", "typography": "Tipografía", "width_is_automatically_optimized": "El ancho se optimiza automáticamente para dispositivos móviles.", "complementary_products": "Los productos complementarios deben configurarse con la aplicación Search & Discovery. [Obtén más información](https://help.shopify.com/manual/online-store/search-and-discovery)", "mobile_column_optimization": "Las columnas se optimizarán automáticamente para dispositivos móviles", "content_width": "El ancho del contenido solo se aplica cuando el ancho de la sección está establecido en ancho completo.", "adjustments_affect_all_content": "Afecta a todo el contenido de este bloque", "responsive_font_sizes": "Los tamaños se adaptan automáticamente a todos los tamaños de pantalla", "buttons": "Botones", "swatches": "Muestras", "variant_settings": "Configuración de las variantes", "background": "Fondo", "cards_layout": "Diseño de tarjetas", "section_layout": "Diseño de la sección", "mobile_size": "Tamaño del móvil", "appearance": "Apariencia", "arrows": "<PERSON><PERSON><PERSON><PERSON>", "body_size": "Tamaño del cuerpo del texto", "bottom_row_appearance": "Apariencia de la fila inferior", "carousel_navigation": "Navegación de carrusel", "carousel_pagination": "Paginación de carrusel", "copyright": "Derechos de autor", "edit_logo_in_theme_settings": "Edita tu logo en [configuración del tema](/editor?context=theme&category=logo%20and%20favicon)", "edit_price_in_theme_settings": "Edita el formato en [configuración del tema](/editor?context=theme&category=currency%20code)", "edit_variants_in_theme_settings": "Edita la variante de estilo en [configuración del tema](/editor?context=theme&category=variants)", "email_signups_create_customer_profiles": "Suscriptores agregados [perfiles de clientes](https://help.shopify.com/manual/customers)", "follow_on_shop_eligiblity": "Para que el botón se muestre, debes tener instalado el canal de Shop y Shop Pay activado. [Obtén más información](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)", "fonts": "<PERSON><PERSON><PERSON>", "grid": "Cuadrícula", "heading_size": "Tamaño del título", "image": "Imagen", "input": "Información", "layout": "Diseño", "link": "Enlace", "link_padding": "<PERSON><PERSON><PERSON>", "localization": "Localización", "logo": "Logo", "margin": "Margen", "media": "Elementos multimedia", "media_1": "Elementos multimedia 1", "media_2": "Elementos multimedia 2", "menu": "Menú", "mobile_layout": "Diseño mó<PERSON>", "padding": "<PERSON><PERSON><PERSON>", "padding_desktop": "Re<PERSON>o del escritorio", "paragraph": "<PERSON><PERSON><PERSON><PERSON>", "policies": "Políticas", "popup": "Ventana emergente", "search": "Buscar", "size": "<PERSON><PERSON><PERSON>", "social_media": "Redes sociales", "submit_button": "Botón de enviar", "text_presets": "Textos predefinidos", "transparent_background": "Fondo transparente", "typography_primary": "Tipografía principal", "typography_secondary": "Tipografía secundaria", "typography_tertiary": "Tipografía terciaria", "mobile_width": "<PERSON>cho para móviles", "width": "<PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "colors": "Colores", "collection_page": "Página de colección", "copyright_info": "Descubre cómo [editar tu declaración de derechos de autor](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)", "customer_account": "Cuenta de cliente", "edit_empty_state_collection_in_theme_settings": "Edita el estado vacío de la colección en [configuración del tema ](/editor?context=theme&category=search)", "home_page": "Página de inicio", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverse_logo_info": "Se utiliza cuando el fondo del encabezado transparente está configurado como Inverso", "manage_customer_accounts": "[Gestionar visibilidad](/admin/settings/customer_accounts) en la configuración de las cuentas de cliente. Las cuentas heredadas no son compatibles.", "manage_policies": "[Gestionar políticas](/admin/settings/legal)", "product_page": "Página de producto", "text": "Texto", "thumbnails": "Miniaturas", "visibility": "Visibilidad", "visible_if_collection_has_more_products": "Visible si la colección tiene más productos que los que se muestran", "grid_layout": "Diseño de cuadrícula", "app_required_for_ratings": "Se requiere una aplicación para las calificaciones de productos. [Obtén más información en](https://help.shopify.com/manual/apps)", "icon": "Ícono", "manage_store_name": "[Gestionar nombre de la tienda](/admin/settings/general?edit=storeName)", "resource_reference_collection_card": "Muestra la colección de la sección principal", "resource_reference_collection_card_image": "Muestra la imagen de la colección principal", "resource_reference_collection_title": "Muestra el título de la colección principal", "resource_reference_product": "Se conecta automáticamente con el producto principal", "resource_reference_product_card": "Muestra el producto de la sección principal", "resource_reference_product_inventory": "Muestra el inventario del producto principal", "resource_reference_product_price": "Muestra el precio del producto principal", "resource_reference_product_recommendations": "Muestra recomendaciones basadas en el producto principal", "resource_reference_product_review": "Muestra reseñas del producto principal", "resource_reference_product_swatches": "Muestra muestras del producto principal", "resource_reference_product_title": "Muestra el título del producto principal", "resource_reference_product_variant_picker": "Muestra variantes del producto principal", "resource_reference_product_media": "Muestra multimedia del producto principal", "product_media": "Elementos multimedia del producto", "section_link": "Enlace de la sección", "gift_card_form_description": "Los clientes pueden enviar tarjetas de regalo al correo electrónico del destinatario junto con un mensaje personal. [Más información](https://help.shopify.com/manual/products/gift-card-products)"}, "html_defaults": {"share_information_about_your": "<p>Comparte información sobre tu marca con los clientes. Describe un producto, comparte anuncios o da la bienvenida a los clientes a tu tienda.</p>"}, "text_defaults": {"button_label": "<PERSON><PERSON><PERSON> ahora", "collapsible_row": "Fila desplegable", "heading": "Encabezado", "email_signup_button_label": "Suscribirse", "accordion_heading": "Encabezado de acordeón", "contact_form_button_label": "Enviar", "popup_link": "Enlace emergente", "sign_up": "Registrarse", "welcome_to_our_store": "Te damos la bienvenida a nuestra tienda", "be_bold": "<PERSON><PERSON> audaz.", "shop_our_latest_arrivals": "¡Compra nuestras últimas novedades!"}, "info": {"carousel_layout_on_mobile": "El carrusel se utiliza en dispositivos móviles", "link_info": "Opcional: hace que los íconos sean navegables", "video_alt_text": "Describe el vídeo para quienes usan tecnologías asistivas", "video_autoplay": "Los vídeos se silenciarán por defecto", "video_external": "Usa una URL de YouTube o Vimeo", "carousel_hover_behavior_not_supported": "No se admite el efecto hover en \"Carrusel\" cuando se elige el tipo \"Carrusel\" en el nivel de sección", "checkout_buttons": "Permite a los compradores pagar más rápido y puede mejorar la conversión. [Obtén más información](https://help.shopify.com/manual/online-store/dynamic-checkout)", "custom_heading": "Encabezado personalizado", "edit_presets_in_theme_settings": "Edita la configuración predeterminada en [configuración del tema](/editor?context=theme&category=typography)", "enable_filtering_info": "Personaliza los filtros con la [aplicación Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)", "grid_layout_on_mobile": "El diseño de cuadrícula se usa para móvil", "logo_font": "Solo se aplica cuando no hay un logo seleccionado", "manage_countries_regions": "[Gestionar países o regiones](/admin/settings/markets)", "manage_languages": "[Gestionar idiomas](/admin/settings/languages)", "transparent_background": "Revisa los formularios en los que el fondo transparente se aplica para facilitar la lectura", "aspect_ratio_adjusted": "Ajustado en algunos diseños", "auto_open_cart_drawer": "Si está habilitado, el carrito lateral se abre automáticamente cuando se agrega un producto al carrito.", "custom_liquid": "Agrega fragmentos de la aplicación u otros códigos para crear personalizaciones avanzadas. [Obtén más información](https://shopify.dev/docs/api/liquid)", "applies_on_image_only": "Solo se aplica a las imágenes", "hover_effects": "Aplica a los productos y tarjetas de colección", "pills_usage": "Se utiliza para filtros aplicados, códigos de descuento y sugerencias de búsqueda"}, "categories": {"basic": "Básico", "collection": "Colección", "collection_list": "Lista de colecciones", "footer": "Pie de página", "forms": "Formularios", "header": "Encabezado", "layout": "Diseño", "links": "Enlaces", "product": "Producto", "product_list": "Colección destacada", "banners": "Banners", "collections": "Colecciones", "custom": "Personalizar", "decorative": "Decorativo", "products": "Productos", "other_sections": "<PERSON><PERSON>", "storytelling": "Narración"}}