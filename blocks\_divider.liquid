

{% render 'divider', id: block.id, settings: block.settings, attributes: true %}

{% schema %}
{
  "name": "t:names.divider",
  "tag": null,
  "settings": [
    {
      "type": "range",
      "id": "thickness",
      "label": "t:settings.thickness",
      "min": 0.5,
      "max": 5,
      "step": 0.5,
      "unit": "px",
      "default": 1
    },
    {
      "type": "select",
      "id": "corner_radius",
      "label": "t:settings.border_radius",
      "options": [
        {
          "value": "square",
          "label": "t:options.square"
        },
        {
          "value": "rounded",
          "label": "t:options.rounded"
        }
      ],
      "default": "square",
      "visible_if": "{{ block.settings.thickness > 1 }}"
    },
    {
      "type": "range",
      "id": "width_percent",
      "label": "t:settings.length",
      "min": 5,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.divider",
      "category": "t:categories.layout"
    }
  ]
}
{% endschema %}
