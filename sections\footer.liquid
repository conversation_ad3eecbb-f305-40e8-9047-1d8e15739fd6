

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
    {% if section.settings.section_height != "" %} {{ section.settings.section_height | prepend: "section--height-" }}{% endif %}
  "
>
  {% render 'background-media',
    background_media: section.settings.background_media,
    background_video: section.settings.video,
    background_video_position: section.settings.video_position,
    background_image: section.settings.background_image,
    background_image_position: section.settings.background_image_position
  %}

  <div
    class="
      spacing-style
      layout-panel-flex
      layout-panel-flex--{{ section.settings.content_direction }}
      section-content-wrapper
      {% if section.settings.vertical_on_mobile %} mobile-column{% endif %}
    "
    style="
      {% render 'layout-panel-style', settings: section.settings %}
      {%  render 'spacing-style', settings: section.settings %}
    "
  >
    {% content_for 'blocks' %}
  </div>
</div>

{% schema %}
{
  "name": "t:names.footer",
  "class": "section-wrapper",
  "tag": "footer",
  "blocks": [
    {
      "type": "_divider"
    },
    {
      "type": "@app"
    },
    {
      "type": "button"
    },
    {
      "type": "email-signup"
    },
    {
      "type": "follow-on-shop"
    },
    {
      "type": "footer-utilities"
    },
    {
      "type": "group"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "menu"
    },
    {
      "type": "payment-icons"
    },
    {
      "type": "text"
    },
    {
      "type": "logo"
    },
    {
      "type": "jumbo-text"
    }
  ],
  "enabled_on": {
    "groups": ["footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "content_direction",
      "label": "t:settings.direction",
      "options": [
        {
          "value": "column",
          "label": "t:options.vertical"
        },
        {
          "value": "row",
          "label": "t:options.horizontal"
        }
      ],
      "default": "column"
    },
    {
      "type": "checkbox",
      "id": "vertical_on_mobile",
      "label": "t:settings.vertical_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "checkbox",
      "id": "align_baseline",
      "label": "t:settings.align_baseline",
      "default": false,
      "visible_if": "{{ section.settings.vertical_alignment == 'flex-end' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment_flex_direction_column",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction != 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment_flex_direction_column",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'column' }}"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "t:settings.height",
      "options": [
        {
          "value": "",
          "label": "t:options.auto"
        },
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        },
        {
          "value": "full-screen",
          "label": "t:options.full_screen"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "background_media",
      "label": "t:settings.background_media",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "none"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:settings.video",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "select",
      "id": "video_position",
      "label": "t:settings.video_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "contain",
          "label": "t:options.contain"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "t:settings.image",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "select",
      "id": "background_image_position",
      "label": "t:settings.image_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "fit",
          "label": "t:options.fit"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
