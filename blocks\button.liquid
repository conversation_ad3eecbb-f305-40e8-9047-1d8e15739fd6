
{% render 'button', link: block.settings.link %}

{% schema %}
{
  "name": "t:names.button",
  "tag": null,
  "settings": [
    {
      "type": "text",
      "id": "label",
      "label": "t:settings.label",
      "default": "t:text_defaults.button_label"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    },
    {
      "type": "checkbox",
      "id": "open_in_new_tab",
      "label": "t:settings.open_new_tab",
      "default": false
    },
    {
      "type": "select",
      "id": "style_class",
      "label": "t:settings.style",
      "options": [
        {
          "value": "button",
          "label": "t:options.primary"
        },
        {
          "value": "button-secondary",
          "label": "t:options.secondary"
        },
        {
          "value": "link",
          "label": "t:options.link"
        }
      ],
      "default": "button"
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width_desktop",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == \"custom\" }}"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:settings.width_mobile",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width_mobile",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width_mobile == \"custom\" }}"
    }
  ],
  "presets": [
    {
      "name": "t:names.button",
      "category": "t:categories.basic",
      "settings": {
        "link": "shopify://collections/all"
      }
    }
  ]
}
{% endschema %}
