{%- comment -%}
  Derives CSS variables from the menu typography settings for 1st level/main menu items.
  Accepts:
     settings {block.settings}
     menu_type {string}: 'drawer' or 'mega_menu'
{%- endcomment -%}

--menu-top-level-font-family: var(--font-{{ settings.type_font_primary_link }}--family);
--menu-top-level-font-size-desktop: {{ settings.type_font_primary_size }}; --menu-top-level-font-style: var(--font-
{{- settings.type_font_primary_link -}}
--style); --menu-top-level-font-weight: var(--font-
{{- settings.type_font_primary_link -}}
--weight); --menu-top-level-font-case:
{%- if settings.type_case_primary_link == 'uppercase' %}uppercase{% else %}none{% endif -%}
;
{% if menu_type == 'drawer' %}
  --menu-top-level-font-size: var(--menu-font-2xl--size); --menu-top-level-font-line-height:
  var(--menu-font-2xl--line-height);
{% else %}
  --menu-top-level-font-size: var(--menu-font-sm--size); --menu-top-level-font-line-height:
  var(--menu-font-sm--line-height);
{% endif %}
--menu-top-level-font-color: var(--color-foreground); --menu-top-level-font-color-rgb: var(--color-foreground-rgb);
