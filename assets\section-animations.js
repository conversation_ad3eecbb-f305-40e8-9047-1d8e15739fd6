import { Component } from '@theme/component';
import { prefersReducedMotion } from '@theme/utilities';

/**
 * A custom element that manages fade-in animations for page sections using Intersection Observer.
 * Provides high-performance, GPU-accelerated animations that respect accessibility preferences.
 * 
 * @extends Component
 */
export class SectionAnimationComponent extends Component {
  /** @type {IntersectionObserver | null} */
  #observer = null;

  /** @type {Set<Element>} */
  #animatedElements = new Set();

  /** @type {boolean} */
  #isInitialized = false;

  connectedCallback() {
    super.connectedCallback();
    
    // Don't animate if user prefers reduced motion
    if (prefersReducedMotion()) {
      this.#showAllElements();
      return;
    }

    this.#initializeAnimations();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.#cleanup();
  }

  /**
   * Initialize the intersection observer and prepare elements for animation
   */
  #initializeAnimations() {
    if (this.#isInitialized) return;

    // Find all animatable elements
    const animatableElements = this.querySelectorAll('[data-animate]');
    
    if (animatableElements.length === 0) return;

    // Set up intersection observer with optimized settings
    this.#observer = new IntersectionObserver(
      this.#handleIntersection.bind(this),
      {
        root: null, // Use viewport as root
        rootMargin: '0px 0px -10% 0px', // Trigger slightly before element is fully visible
        threshold: [0, 0.1] // Minimal thresholds for performance
      }
    );

    // Prepare elements and start observing
    animatableElements.forEach(element => {
      this.#prepareElement(element);
      this.#observer.observe(element);
    });

    this.#isInitialized = true;
  }

  /**
   * Prepare an element for animation by setting initial styles
   * @param {Element} element - The element to prepare
   */
  #prepareElement(element) {
    const animationType = element.dataset.animate || 'fade-up';
    
    // Set initial state based on animation type
    switch (animationType) {
      case 'fade-up':
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        break;
      case 'fade-down':
        element.style.opacity = '0';
        element.style.transform = 'translateY(-20px)';
        break;
      case 'fade-left':
        element.style.opacity = '0';
        element.style.transform = 'translateX(20px)';
        break;
      case 'fade-right':
        element.style.opacity = '0';
        element.style.transform = 'translateX(-20px)';
        break;
      case 'fade':
      default:
        element.style.opacity = '0';
        break;
    }

    // Add CSS class for transition properties
    element.classList.add('animate-element');
    
    // Inform browser of upcoming changes for GPU acceleration
    element.style.willChange = 'transform, opacity';
  }

  /**
   * Handle intersection observer callbacks
   * @param {IntersectionObserverEntry[]} entries - The intersection entries
   */
  #handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio > 0.1) {
        this.#animateElement(entry.target);
      }
    });
  }

  /**
   * Animate an element into view
   * @param {Element} element - The element to animate
   */
  #animateElement(element) {
    if (this.#animatedElements.has(element)) return;

    // Mark as animated to prevent re-animation
    this.#animatedElements.add(element);
    
    // Stop observing this element
    this.#observer?.unobserve(element);

    // Add animation class and reset transform/opacity
    element.classList.add('animate-in');
    element.style.opacity = '';
    element.style.transform = '';

    // Clean up will-change after animation completes
    const cleanup = () => {
      element.style.willChange = '';
      element.removeEventListener('transitionend', cleanup);
    };
    
    element.addEventListener('transitionend', cleanup, { once: true });
    
    // Fallback cleanup in case transitionend doesn't fire
    setTimeout(cleanup, 1000);
  }

  /**
   * Show all elements immediately (for reduced motion preference)
   */
  #showAllElements() {
    const animatableElements = this.querySelectorAll('[data-animate]');
    animatableElements.forEach(element => {
      element.style.opacity = '';
      element.style.transform = '';
      element.classList.add('animate-element', 'animate-in');
    });
  }

  /**
   * Clean up observer and event listeners
   */
  #cleanup() {
    if (this.#observer) {
      this.#observer.disconnect();
      this.#observer = null;
    }
    this.#animatedElements.clear();
    this.#isInitialized = false;
  }
}

// Register the custom element
customElements.define('section-animation', SectionAnimationComponent);
