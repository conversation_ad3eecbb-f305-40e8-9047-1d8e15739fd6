

{% render 'cart-products' %}

{% stylesheet %}
  .cart-page--empty .cart-items__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-block-start: 0;
    text-align: center;
  }

  .cart-page__title + .cart-page__items {
    margin-block-start: var(--margin-lg);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.cart_products",
  "tag": null,
  "settings": [
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 8,
      "max": 36,
      "step": 4,
      "unit": "px",
      "default": 24
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "portrait",
          "label": "t:options.portrait"
        },
        {
          "value": "square",
          "label": "t:options.square"
        }
      ],
      "default": "adapt",
      "label": "t:settings.aspect_ratio"
    },
    {
      "type": "checkbox",
      "id": "dividers",
      "label": "t:settings.dividers",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "vendor",
      "label": "t:settings.vendor",
      "default": false
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.cart_items"
    }
  ]
}
{% endschema %}
