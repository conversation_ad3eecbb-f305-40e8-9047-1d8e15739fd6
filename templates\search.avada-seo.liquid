{%- layout none -%}

{%- assign pageurl = content_for_header| split:'"pageurl":"' | last | split:'"' | first -%}

{%- if pageurl contains 'preview=1' -%}
{%- assign lim = 1 -%}
{%- else -%}
{%- assign lim = 1000 -%}
{%- endif -%}

{%- if pageurl contains 'type=collection' -%}
{%- paginate collections by 1000 -%}
{%- assign page_count = paginate.pages -%}
{%- endpaginate -%}
{
"pageCount": {{ page_count }},
"items":
[
{%- paginate collections by 1000 -%}
{% for collection in collections %}
{
{%- if pageurl contains 'preview=1' -%}
  "all_products_count": {{ collection.all_products_count | json }},
  "current_type": {{ collection.current_type | json }},
  "current_vendor": {{ collection.current_vendor | json }},
  "default_sort_by": {{ collection.default_sort_by | json }},
  "description": {{ collection.description | json }},
  "handle": {{ collection.handle | json }},
  "id": {{ collection.id | json }},
  "image": {{ collection.image | json }},
  "next_product": {{ collection.next_product | json }},
  "previous_product": {{ collection.previous_product | json }},
  "products_count": {{ collection.products_count | json }},
  "sort_by": {{ collection.sort_by | json }},
{%- endif -%}
{%- if pageurl contains 'count=1' -%}
  "image": {{ collection.image | json }},
{%- endif -%}
  "title": {{ collection.title | strip_html | strip_newlines | strip | escape | json }},
  "url": {{ collection.url | json }}
}{%- if forloop.last == false -%},{% endif -%}
{% endfor %}
{%- endpaginate -%}
]
}
{%- endif -%}

{%- if pageurl contains 'type=product' -%}
{%- paginate search.results by lim -%}
{%- assign page_count = paginate.pages -%}
{%- endpaginate -%}
{
"pageCount": {{ page_count }},
"items":
[
{%- paginate search.results by lim -%}
{% for product in search.results %}
{
{%- if pageurl contains 'preview=1' or pageurl contains 'q=id' -%}
  "available": {{ product.available | json }},
  "collections": {{ product.collections | json }},
  "compare_at_price": {{ product.compare_at_price | json }},
  "compare_at_price_max": {{ product.compare_at_price_max | json }},
  "compare_at_price_min": {{ product.compare_at_price_min | json }},
  "compare_at_price_varies": {{ product.compare_at_price_varies | json }},
  "content": {{ product.content | strip_html | strip | json }},
  "created_at": {{ product.created_at | json }},
  "description": {{ product.description | json }},
  "featured_image": {{ product.featured_image | json }},
  "featured_media": {{ product.featured_media | json }},
  "first_available_variant": {{ product.first_available_variant | json }},
  "handle": {{ product.handle | json }},
  "has_only_default_variant": {{ product.has_only_default_variant | json }},
  "id": {{ product.id | json }},
  "images": {{ product.images | json }},
  "options": {{ product.options | json }},
  "options_by_name": {{ product.options_by_name | json }},
  "options_with_values": {{ product.options_with_values | json }},
  "price": {{ product.price | json }},
  "price_max": {{ product.price_max | json }},
  "price_min": {{ product.price_min | json }},
  "price_varies": {{ product.price_varies | json }},
  "published_at": {{ product.published_at | json }},
  "selected_variant": {{ product.selected_variant | json }},
  "selected_or_first_available_variant": {{ product.selected_or_first_available_variant | json }},
  "tags": {{ product.tags | json }},
  "type": {{ product.type | json }},
  "variants": {{ product.variants | json }},
  "vendor": {{ product.vendor | json }},
{%- endif -%}
{%- if pageurl contains 'count=1' -%}
  "images": [
  {% for image in product.images %}
    "{{ image | img_url: 'master' }}"
    {%- if forloop.last == false -%},{% endif -%}
  {% endfor %}
  ],
{%- endif -%}
  "title": {{ product.title | strip_html | strip_newlines | strip | escape | json }},
  "url": {{ product.url | json }}
}{%- if forloop.last == false -%},{% endif -%}
{% endfor %}
{%- endpaginate -%}
]
}
{%- endif -%}

{%- if pageurl contains 'type=article' -%}
{%- paginate search.results by lim -%}
{%- assign page_count = paginate.pages -%}
{%- endpaginate -%}
{
"pageCount": {{ page_count }},
"items":
[
{%- paginate search.results by lim -%}
{% for article in search.results %}
{
{%- if pageurl contains 'preview=1' -%}
  "author": {{ article.author | json }},
  "comments": {{ article.comments | json }},
  "comments_count": {{ article.comments_count | json }},
  "comments_enabled": {{ article.comments_enabled? | json }},
  "comment_post_url": {{ article.comment_post_url | json }},
  "content": {{ article.content | strip_html | strip | json }},
  "created_at": {{ article.created_at | json }},
  "excerpt": {{ article.excerpt | json }},
  "excerpt_or_content": {{ article.excerpt_or_content | strip_html | strip | json }},
  "handle": {{ article.handle | json }},
  "id": {{ article.id | json }},
  "image": {{ article.image | json }},
  "moderated": {{ article.moderated? | json }},
  "published_at": {{ article.published_at | json }},
  "tags": {{ article.tags | json }},
  "updated_at": {{ article.updated_at | json }},
  "user": {{ article.user | json }},
{%- endif -%}
{%- if pageurl contains 'count=1' -%}
  "image": {{ article.image | json }},
{%- endif -%}
  "title": {{ article.title | strip_html | strip_newlines | strip | escape | json }},
  "url": {{ article.url | json }}
}{%- if forloop.last == false -%},{% endif -%}
{% endfor %}
{%- endpaginate -%}
]
}
{%- endif -%}

{%- if pageurl contains 'type=page' -%}
{%- paginate search.results by lim -%}
{%- assign page_count = paginate.pages -%}
{%- endpaginate -%}
{
"pageCount": {{ page_count }},
"items":
[
{%- paginate search.results by lim -%}
{% for page in search.results %}
{
{%- if pageurl contains 'preview=1' -%}
  "author": {{ page.author | json }},
  "content": {{ page.content | strip_html | strip | json }},
  "handle": {{ page.handle | json }},
  "id": {{ page.id | json }},
  "published_at": {{ page.published_at | json }},
{%- endif -%}
  "title": {{ page.title | strip_html | strip_newlines | strip | escape | json }},
  "url": {{ page.url | json }}
}{%- if forloop.last == false -%},{%- endif -%}
{% endfor %}
{%- endpaginate -%}
]
}
{%- endif -%}

{%- if pageurl contains 'type=shop' -%}
{
"pageCount": 1,
"items":
[
{
  "address": {{ shop.address | json }},
  "address.city": {{ shop.address.city | json }},
  "address.country": {{ shop.address.country | json }},
  "address.country_upper": {{ shop.address.country_upper | json }},
  "address.province": {{ shop.address.province | json }},
  "address.province_code": {{ shop.address.province_code | json }},
  "address.street": {{ shop.address.street | json }},
  "address.summary": {{ shop.address.summary | json }},
  "address.zip": {{ shop.address.zip | json }},
  "collections_count": {{ shop.collections_count | json }},
  "currency": {{ shop.currency | json }},
  "description": {{ shop.description | json }},
  "domain": {{ shop.domain | json }},
  "email": {{ shop.email | json }},
  "enabled_currencies": {{ shop.enabled_currencies | json }},
  "enabled_payment_types": {{ shop.enabled_payment_types | json }},
  "metafields": {{ shop.metafields | json }},
  "money_format": {{ shop.money_format | json }},
  "money_with_currency_format": {{ shop.money_with_currency_format | json }},
  "name": {{ shop.name | json }},
  "password_message": {{ shop.password_message | json }},
  "permanent_domain": {{ shop.permanent_domain | json }},
  "phone": {{ shop.phone | json }},
  "policies": {{ shop.policies | json }},
  "privacy_policy": {{ shop.privacy_policy | json }},
  "published_locales": {{ shop.published_locales | json }},
  "refund_policy": {{ shop.refund_policy | json }},
  "shipping_policy": {{ shop.shipping_policy | json }},
  "terms_of_service": {{ shop.terms_of_service | json }},
  "products_count": {{ shop.products_count | json }},
  "secure_url": {{ shop.secure_url | json }},
  "taxes_included": {{ shop.taxes_included | json }},
  "types": {{ shop.types | json }},
  "url": {{ shop.url | json }},
  "vendors": {{ shop.vendors | json }}
}
]
}
{%- endif -%}