/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "categories": {
    "banners": "Banners",
    "basic": "Basic",
    "collection": "Collection",
    "collections": "Collections",
    "collection_list": "Collection list",
    "custom": "Custom",
    "decorative": "Decorative",
    "footer": "Footer",
    "forms": "Forms",
    "header": "Header",
    "layout": "Layout",
    "links": "Links",
    "product": "Product",
    "products": "Products",
    "product_list": "Featured collection",
    "other_sections": "Other",
    "storytelling": "Storytelling"
  },
  "content": {
    "visible_if_collection_has_more_products": "Visible if collection has more products than shown",
    "adjustments_affect_all_content": "Applies to all content in this block",
    "advanced": "Advanced",
    "appearance": "Appearance",
    "arrows": "Arrows",
    "background": "Background",
    "background_image": "Background image",
    "background_video": "Background video",
    "block_size": "Block size",
    "body_size": "Body size",
    "borders": "Borders",
    "bottom_row_appearance": "Bottom row appearance",
    "buttons": "Buttons",
    "cards_layout": "Cards layout",
    "carousel": "Carousel",
    "carousel_navigation": "Carousel navigation",
    "carousel_pagination": "Carousel pagination",
    "colors": "Colors",
    "collection_page": "Collection page",
    "complementary_products": "Complementary products must be set up using the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "content_width": "Content width only applies when the section width is set to full width.",
    "copyright": "Copyright",
    "customer_account": "Customer account",
    "describe_the_video_for": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "edit_empty_state_collection_in_theme_settings": "Edit empty state collection in [theme settings](/editor?context=theme&category=search)",
    "edit_logo_in_theme_settings": "Edit logo in [theme settings](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Edit price formatting in [theme settings](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Edit variant styling in [theme settings](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Signups add [customer profiles](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "For the button to show, the Shop channel must be installed and Shop Pay activated. [Learn more](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "gift_card_form_description": "Customers can send gift cards to a recipient's email with a personal message. [Learn more](https://help.shopify.com/manual/products/gift-card-products)",
    "fonts": "Fonts",
    "grid": "Grid",
    "grid_layout": "Grid layout",
    "heading_size": "Heading size",
    "home_page": "Home page",
    "icon": "Icon",
    "image": "Image",
    "images": "Images",
    "input": "Input",
    "inverse_logo_info": "Used when transparent header background is set to Inverse",
    "layout": "Layout",
    "link": "Link",
    "link_padding": "Link padding",
    "localization": "Localization",
    "logo": "Logo",
    "manage_customer_accounts": "[Manage visibility](/admin/settings/customer_accounts) in customer account settings. Legacy accounts not supported.",
    "manage_policies": "[Manage policies](/admin/settings/legal)",
    "manage_store_name": "[Manage store name](/admin/settings/general?edit=storeName)",
    "margin": "Margin",
    "media": "Media",
    "media_1": "Media 1",
    "media_2": "Media 2",
    "menu": "Menu",
    "mobile_column_optimization": "Columns will automatically optimize for mobile",
    "mobile_layout": "Mobile layout",
    "mobile_size": "Mobile size",
    "mobile_width": "Mobile width",
    "padding": "Padding",
    "padding_desktop": "Desktop padding",
    "paragraph": "Paragraph",
    "policies": "Policies",
    "popup": "Popup",
    "product_media": "Product media",
    "product_page": "Product page",
    "responsive_font_sizes": "Sizes automatically scale for all screen sizes",
    "resource_reference_collection_card": "Displays collection from parent section",
    "resource_reference_collection_card_image": "Displays image from parent collection",
    "resource_reference_collection_title": "Displays title from parent collection",
    "resource_reference_product": "Auto connects to parent product",
    "resource_reference_product_card": "Displays product from parent section",
    "resource_reference_product_inventory": "Displays inventory from parent product",
    "resource_reference_product_media": "Displays media from parent product",
    "resource_reference_product_price": "Displays price from parent product",
    "resource_reference_product_recommendations": "Displays recommendations based on parent product",
    "resource_reference_product_review": "Displays reviews from parent product",
    "resource_reference_product_swatches": "Displays swatches from parent product",
    "resource_reference_product_title": "Displays title from parent product",
    "resource_reference_product_variant_picker": "Displays variants from parent product",
    "search": "Search",
    "section_layout": "Section layout",
    "section_link": "Section link",
    "section_size": "Section size",
    "size": "Size",
    "slideshow_width": "Slide width",
    "social_media": "Social media",
    "submissions_are_sent_to_store_email_address": "Submissions are sent to your store's [sender email address](https://admin.shopify.com/settings/notifications)",
    "submit_button": "Submit button",
    "swatches": "Swatches",
    "text": "Text",
    "text_presets": "Text presets",
    "thumbnails": "Thumbnails",
    "transparent_background": "Transparent background",
    "typography": "Typography",
    "typography_primary": "Primary typography",
    "typography_secondary": "Secondary typography",
    "typography_tertiary": "Tertiary typography",
    "variant_settings": "Variant settings",
    "visibility": "Visibility",
    "width": "Width",
    "width_is_automatically_optimized": "Width is automatically optimized for mobile.",
    "app_required_for_ratings": "An app is required for product ratings. [Learn more](https://help.shopify.com/manual/apps)"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>"
  },
  "info": {
    "applies_on_image_only": "Applies to images only",
    "aspect_ratio_adjusted": "Adjusted in some layouts",
    "auto_open_cart_drawer": "When enabled, the cart drawer will automatically open when a product is added to cart.",
    "carousel_hover_behavior_not_supported": "\"Carousel\" hover is not supported when \"Carousel\" type is selected at the section level",
    "carousel_layout_on_mobile": "Carousel is used on mobile",
    "checkout_buttons": "Allows buyers to check out faster and can improve conversion. [Learn more](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Custom heading",
    "custom_liquid": "Add app snippets or other code to create advanced customizations. [Learn more](https://shopify.dev/docs/api/liquid)",
    "edit_presets_in_theme_settings": "Edit presets in [theme settings](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Customize filters with the [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "grid_layout_on_mobile": "Grid layout is used for mobile",
    "hover_effects": "Applies to product and collection cards",
    "link_info": "Optional: makes icon clickable",
    "logo_font": "Applies only when a logo is not selected",
    "manage_countries_regions": "[Manage countries/regions](/admin/settings/markets)",
    "manage_languages": "[Manage languages](/admin/settings/languages)",
    "transparent_background": "Review each template where transparent background is applied for readability",
    "video_alt_text": "Describe the video for assistive tech users",
    "video_autoplay": "Videos will be muted by default",
    "video_external": "Use a YouTube or Vimeo URL",
    "pills_usage": "Used for applied filters, discount codes, and search suggestions"

  },
  "names": {
    "product_title": "Product title",
    "custom_liquid": "Custom liquid",
    "404": "404",
    "accelerated_checkout": "Accelerated checkout",
    "accordion": "Accordion",
    "accordion_row": "Accordion row",
    "add_to_cart": "Add to cart",
    "alternating_content_rows": "Alternating rows",
    "animations": "Animations",
    "announcement": "Announcement",
    "announcement_bar": "Announcement bar",
    "badges": "Badges",
    "blog": "Blog",
    "blog_post": "Blog post",
    "blog_posts": "Blog posts",
    "borders": "Borders",
    "button": "Button",
    "buttons": "Buttons",
    "caption": "Caption",
    "cart": "Cart",
    "cart_items": "Cart items",
    "cart_products": "Cart products",
    "cart_title": "Cart",
    "collapsible_row": "Collapsible row",
    "collection": "Collection",
    "collection_card": "Collection card",
    "collection_card_image": "Image",
    "collection_columns": "Collection columns",
    "collection_container": "Collection",
    "collection_description": "Collection description",
    "collection_image": "Collection image",
    "collection_info": "Collection info",
    "collection_list": "Collection list",
    "collection_title": "Collection title",
    "collections": "Collections",
    "collection_links": "Collection links",
    "collection_links_spotlight": "Collection links: Spotlight",
    "collection_links_text": "Collection links: Text",
    "collections_bento": "Collection list: Bento",
    "collections_carousel": "Collection list: Carousel",
    "collections_editorial": "Collection list: Editorial",
    "collections_grid": "Collection list: Grid",
    "colors": "Colors",
    "contact_form": "Contact form",
    "content": "Content",
    "content_grid": "Content grid",
    "copyright": "Copyright",
    "count": "Count",
    "custom_section": "Custom section",
    "description": "Description",
    "details": "Details",
    "divider": "Divider",
    "divider_section": "Divider",
    "drawers": "Drawers",
    "editorial": "Editorial",
    "editorial_jumbo_text": "Editorial: Jumbo text",
    "email_signup": "Email signup",
    "facets": "Facets",
    "faq_section": "FAQ",
    "featured_collection": "Featured collection",
    "featured_product": "Product highlight",
    "featured_image": "Featured image",
    "filters": "Filtering and sorting",
    "follow_on_shop": "Follow on Shop",
    "footer": "Footer",
    "footer_utilities": "Footer utilities",
    "grid_layout_selector": "Grid layout selector",
    "group": "Group",
    "header": "Header",
    "heading": "Heading",
    "hero": "Hero",
    "hero_marquee": "Hero: Marquee",
    "icon": "Icon",
    "icons": "Icons",
    "icons_with_text": "Icons with text",
    "image": "Image",
    "image_with_text": "Image with text",
    "input": "Input",
    "input_fields": "Input fields",
    "inputs": "Inputs",
    "jumbo_text": "Jumbo text",
    "large_logo": "Large logo",
    "list_items": "List items",
    "local_pickup": "Local pickup",
    "logo": "Logo",
    "logo_and_favicon": "Logo and favicon",
    "magazine_grid": "Magazine grid",
    "marquee": "Marquee",
    "marquee_section": "Marquee",
    "media": "Media",
    "media_with_text": "Media with text",
    "menu": "Menu",
    "mobile_layout": "Mobile layout",
    "overlapping_blocks": "Overlapping blocks",
    "page": "Page",
    "page_content": "Content",
    "page_layout": "Page layout",
    "payment_icons": "Payment icons",
    "policy_list": "Policy links",
    "popovers": "Popovers",
    "popup_link": "Popup link",
    "predictive_search": "Search popover",
    "predictive_search_empty": "Predictive search empty",
    "price": "Price",
    "prices": "Prices",
    "primary_button": "Primary button",
    "product": "Product",
    "product_buy_buttons": "Buy buttons",
    "product_card": "Product card",
    "product_card_media": "Media",
    "product_card_rendering": "Product card rendering",
    "product_cards": "Product cards",
    "product_description": "Description",
    "product_grid": "Grid",
    "product_grid_main": "Product grid",
    "product_image": "Product image",
    "product_information": "Product information",
    "product_list": "Featured collection",
    "product_list_button": "View all button",
    "product_media": "Product media",
    "product_price": "Price",
    "product_recommendations": "Recommended products",
    "product_review_stars": "Review stars",
    "product_variant_picker": "Variant picker",
    "products_carousel": "Featured collection: Carousel",
    "products_editorial": "Featured collection: Editorial",
    "products_grid": "Featured collection: Grid",
    "product_inventory": "Product inventory",
    "pull_quote": "Pull quote",
    "quantity": "Quantity",
    "read_only": "Read only",
    "row": "Row",
    "search": "Search",
    "search_input": "Search input",
    "search_results": "Search results",
    "secondary_button": "Secondary button",
    "section": "Section",
    "selected_variants": "Selected variants",
    "shop_the_look": "Shop the look",
    "size": "Size",
    "slide": "Slide",
    "slideshow": "Slideshow",
    "slideshow_controls": "Slideshow controls",
    "social_link": "Social link",
    "social_media_links": "Social media links",
    "spacer": "Spacer",
    "spacing": "Spacing",
    "split_showcase": "Split showcase",
    "steps": "Steps",
    "styles": "Styles",
    "submit_button": "Submit button",
    "summary": "Summary",
    "swatches": "Swatches",
    "testimonials": "Testimonials",
    "text": "Text",
    "title": "Title",
    "typography": "Typography",
    "utilities": "Utilities",
    "variant_pickers": "Variant pickers",
    "variants": "Variants",
    "video": "Video",
    "video_section": "Video",
    "view_all_button": "View all",
    "pills": "Pills"
  },
  "options": {
    "above_carousel": "Above carousel",
    "accent": "Accent",
    "adapt_to_image": "Adapt to image",
    "all": "All",
    "always": "Always",
    "apple": "Apple",
    "arrow": "Arrow",
    "arrows": "Arrows",
    "arrows_large": "Large arrows",
    "aspect_ratio": "Aspect ratio",
    "auto": "Auto",
    "balance": "Balance",
    "banana": "Banana",
    "below_image": "Below image",
    "bento": "Bento",
    "black": "Black",
    "bluesky": "Bluesky",
    "blur": "Blur",
    "body": "Body",
    "body_large": "Body (Large)",
    "body_regular": "Body (Regular)",
    "body_small": "Body (Small)",
    "bold": "Bold",
    "bottle": "Bottle",
    "bottom": "Bottom",
    "bottom_left": "Bottom left",
    "bottom_right": "Bottom right",
    "box": "Box",
    "button": "Button",
    "button_primary": "Primary button",
    "button_secondary": "Secondary button",
    "buttons": "Buttons",
    "capitalize": "Capitalize",
    "caption": "Caption",
    "caret": "Caret",
    "carousel": "Carousel",
    "carrot": "Carrot",
    "center": "Center",
    "chat_bubble": "Chat bubble",
    "check_box": "Check box",
    "chevron": "Chevron",
    "chevron_large": "Large chevrons",
    "chevron_left": "Chevron left",
    "chevron_right": "Chevron right",
    "chevrons": "Chevrons",
    "circle": "Circle",
    "classic": "Classic",
    "clipboard": "Clipboard",
    "collection_images": "Collection images",
    "color": "Color",
    "compact": "Compact",
    "complementary": "Complementary",
    "contain": "Contain",
    "counter": "Counter",
    "cover": "Cover",
    "crop_to_fit": "Crop to fit",
    "custom": "Custom",
    "dairy": "Dairy",
    "dairy_free": "Dairy free",
    "default": "Default",
    "diamond": "Diamond",
    "dissolve": "Dissolve",
    "dots": "Dots",
    "dotted": "Dotted",
    "down": "Down",
    "drawer": "Drawer",
    "dropdowns": "Dropdowns",
    "dryer": "Dryer",
    "editorial": "Editorial",
    "end": "End",
    "extra_large": "Extra large",
    "extra_small": "Extra small",
    "eye": "Eye",
    "facebook": "Facebook",
    "featured_collections": "Featured collections",
    "featured_products": "Featured products",
    "fill": "Fill",
    "fire": "Fire",
    "fit": "Fit",
    "fit_content": "Fit",
    "fixed": "Fixed",
    "font_primary": "Primary",
    "font_secondary": "Secondary",
    "font_tertiary": "Tertiary",
    "forward": "Forward",
    "full": "Full",
    "full_and_page": "Full background, page-width content",
    "full_and_page_offset_left": "Full background, page-width content, offset left",
    "full_and_page_offset_right": "Full background, page-width content, offset right",
    "full_screen": "Full screen",
    "gluten_free": "Gluten free",
    "gradient": "Gradient",
    "grid": "Grid",
    "h1": "Heading 1",
    "h2": "Heading 2",
    "h3": "Heading 3",
    "h4": "Heading 4",
    "h5": "Heading 5",
    "h6": "Heading 6",
    "heading": "Heading",
    "heading_extra_large": "Heading (Extra large)",
    "heading_extra_small": "Heading (Extra small)",
    "heading_large": "Heading (Large)",
    "heading_regular": "Heading (Regular)",
    "heading_small": "Heading (Small)",
    "heart": "Heart",
    "heavy": "Heavy",
    "hidden": "Hidden",
    "hint": "Hint",
    "horizontal": "Horizontal",
    "icon": "Icon",
    "image": "Image",
    "input": "Input",
    "inside_carousel": "Inside carousel",
    "instagram": "Instagram",
    "inverse_large": "Inverse large",
    "inverse": "Inverse",
    "iron": "Iron",
    "landscape": "Landscape",
    "large": "Large",
    "large_arrows": "Large arrows",
    "large_chevrons": "Large chevrons",
    "leaf": "Leaf",
    "leather": "Leather",
    "left": "Left",
    "lg": "LG",
    "lift": "Lift",
    "light": "Light",
    "lightning_bolt": "Lightning bolt",
    "link": "Link",
    "linkedin": "LinkedIn",
    "lipstick": "Lipstick",
    "lock": "Lock",
    "loose": "Loose",
    "lowercase": "lowercase",
    "m": "M",
    "maintain_aspect_ratio": "Maintain aspect ratio",
    "map_pin": "Map pin",
    "media_first": "Media first",
    "media_second": "Media second",
    "medium": "Medium",
    "modal": "Modal",
    "narrow": "Narrow",
    "never": "Never",
    "next_to_carousel": "Next to carousel",
    "none": "None",
    "normal": "Normal",
    "nowrap": "No wrap",
    "numbers": "Numbers",
    "nut_free": "Nut free",
    "off": "Off",
    "off_media": "Off media",
    "offset_left": "Offset left",
    "offset_right": "Offset right",
    "on_image": "On image",
    "on_media": "On media",
    "on_scroll_up": "On scroll up",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "outline": "Outline",
    "page": "Page",
    "page_center_aligned": "Page, center aligned",
    "page_left_aligned": "Page, left aligned",
    "page_right_aligned": "Page, right aligned",
    "pants": "Pants",
    "paragraph": "Paragraph",
    "parallelogram": "Parallelogram",
    "paw_print": "Paw print",
    "pepper": "Pepper",
    "percent": "Percent",
    "perfume": "Perfume",
    "pill": "Pill",
    "pills": "Pills",
    "pinterest": "Pinterest",
    "pixel": "Pixel",
    "plane": "Plane",
    "plant": "Plant",
    "plus": "Plus",
    "portrait": "Portrait",
    "pretty": "Pretty",
    "preview": "Preview",
    "price": "Price",
    "price_tag": "Price tag",
    "primary": "Primary",
    "primary_style": "Primary style",
    "question_mark": "Question mark",
    "rectangle": "Rectangle",
    "recycle": "Recycle",
    "regular": "Regular",
    "related": "Related",
    "return": "Return",
    "reveal": "Reveal",
    "reverse": "Reverse",
    "rich_text": "Rich text",
    "right": "Right",
    "rounded": "Rounded",
    "ruler": "Ruler",
    "s": "S",
    "scale": "Scale",
    "secondary": "Secondary",
    "secondary_style": "Secondary style",
    "semibold": "Semibold",
    "sentence": "Sentence",
    "serving_dish": "Serving dish",
    "shaded": "Shaded",
    "shirt": "Shirt",
    "shoe": "Shoe",
    "show_second_image": "Show second image",
    "silhouette": "Silhouette",
    "single": "Single",
    "slide_left": "Slide left",
    "slide_up": "Slide up",
    "small": "Small",
    "snapchat": "Snapchat",
    "snowflake": "Snowflake",
    "social_bluesky": "Social: Bluesky",
    "social_facebook": "Social: Facebook",
    "social_instagram": "Social: Instagram",
    "social_linkedin": "Social: LinkedIn",
    "social_pinterest": "Social: Pinterest",
    "social_snapchat": "Social: Snapchat",
    "social_spotify": "Social: Spotify",
    "social_threads": "Social: Threads",
    "social_tiktok": "Social: TikTok",
    "social_tumblr": "Social: Tumblr",
    "social_twitter": "Social: X (Twitter)",
    "social_whatsapp": "Social: WhatsApp",
    "social_vimeo": "Social: Vimeo",
    "social_youtube": "Social: YouTube",
    "solid": "Solid",
    "space_between": "Space between",
    "spotify": "Spotify",
    "spotlight": "Spotlight",
    "square": "Square",
    "stack": "Stack",
    "standard": "Standard",
    "star": "Star",
    "start": "Start",
    "stopwatch": "Stopwatch",
    "subheading": "Subheading",
    "subtle_zoom": "Zoom",
    "swatches": "Swatches",
    "tertiary": "Tertiary",
    "text": "Text",
    "text_only": "Text only",
    "thin": "Thin",
    "threads": "Threads",
    "thumbnails": "Thumbnails",
    "tight": "Tight",
    "tiktok": "TikTok",
    "top": "Top",
    "top_left": "Top left",
    "top_right": "Top right",
    "truck": "Truck",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Underline",
    "up": "Up",
    "uppercase": "Uppercase",
    "vertical": "Vertical",
    "video": "Video",
    "video_external_url": "External URL",
    "video_uploaded": "Uploaded",
    "vimeo": "Vimeo",
    "washing": "Washing",
    "wide": "Wide",
    "youtube": "YouTube"
  },
  "settings": {
    "accordion": "Accordion",
    "account": "Account",
    "alignment": "Alignment",
    "alignment_mobile": "Mobile alignment",
    "align_baseline": "Align text baseline",
    "animation_repeat": "Repeat animation",
    "add_discount_code": "Allow discounts in cart",
    "always_stack_buttons": "Always stack buttons",
    "aspect_ratio": "Aspect ratio",
    "auto_rotate_announcements": "Auto-rotate announcements",
    "auto_rotate_slides": "Auto-rotate slides",
    "autoplay": "Autoplay",
    "background": "Background",
    "background_color": "Background color",
    "background_overlay": "Background overlay",
    "badge_corner_radius": "Corner radius",
    "background_media": "Background media",
    "badge_position": "Position on cards",
    "badge_sale_color_scheme": "Sale",
    "badge_sold_out_color_scheme": "Sold out",
    "behavior": "Behavior",
    "blur": "Shadow blur",
    "blurred_reflection": "Blurred reflection",
    "border": "Border",
    "border_opacity": "Border opacity",
    "border_radius": "Corner radius",
    "border_style": "Border style",
    "border_thickness": "Border thickness",
    "border_width": "Border thickness",
    "borders": "Borders",
    "bottom": "Bottom",
    "bottom_row": "Bottom row",
    "bottom_padding": "Bottom padding",
    "button": "Button",
    "button_text_case": "Text case",
    "button_text_weight": "Text weight",
    "card_hover_effect": "Card hover effect",
    "card_image_height": "Product image height",
    "card_size": "Card size",
    "carousel_on_mobile": "Carousel on mobile",
    "cart_count": "Cart count",
    "cart_items": "Cart items",
    "cart_related_products": "Related products",
    "cart_title": "Cart",
    "cart_total": "Cart total",
    "cart_type": "Type",
    "auto_open_cart_drawer": "\"Add to cart\" auto-opens drawer",
    "case": "Case",
    "checkout_buttons": "Accelerated checkout buttons",
    "collection": "Collection",
    "collection_count": "Collection count",
    "collection_list": "Collections",
    "collection_templates": "Collection templates",
    "collection_title_case": "Collection title case",
    "color": "Color",
    "color_scheme": "Color scheme",
    "colors": "Colors",
    "columns": "Columns",
    "content": "Content",
    "content_alignment": "Content alignment",
    "content_direction": "Content direction",
    "content_position": "Content position",
    "content_width": "Content width",
    "corner_radius": "Corner radius",
    "country_region": "Country/Region",
    "cover_image": "Cover image",
    "cover_image_size": "Cover image size",
    "currency_code": "Currency code",
    "custom_height": "Custom height",
    "custom_minimum_height": "Custom minimum height",
    "custom_mobile_size": "Custom mobile size",
    "custom_mobile_width": "Custom mobile width",
    "custom_width": "Custom width",
    "custom_liquid": "Liquid code",
    "default": "Default",
    "default_logo": "Default logo",
    "desktop_height": "Desktop height",
    "direction": "Direction",
    "display": "Display",
    "divider": "Divider",
    "divider_color": "Divider",
    "divider_thickness": "Divider thickness",
    "divider_width": "Divider width",
    "dividers": "Dividers",
    "drop_shadow": "Drop shadow",
    "effects": "Effects",
    "empty_state_collection": "Empty state collection",
    "empty_state_collection_info": "Shown before a search is entered",
    "enable_filtering": "Filters",
    "enable_grid_density": "Grid layout control",
    "enable_sorting": "Sorting",
    "enable_sticky_content": "Sticky content on desktop",
    "enable_video_looping": "Video looping",
    "enable_zoom": "Enable zoom",
    "equal_columns": "Equal columns",
    "error_color": "Error",
    "expand_first_group": "Expand first group",
    "extend_media_to_screen_edge": "Extend media to screen edge",
    "extend_summary": "Extend to screen edge",
    "extra_large": "Extra large",
    "extra_small": "Extra small",
    "favicon": "Favicon",
    "filter_style": "Filter style",
    "first_row_media_position": "First row media position",
    "fixed_height": "Pixel height",
    "fixed_width": "Pixel width",
    "flag": "Flag",
    "font": "Font",
    "font_family": "Font family",
    "font_price": "Price font",
    "font_weight": "Font weight",
    "full_width_first_image": "Full width first image",
    "full_width_on_mobile": "Full width on mobile",
    "gap": "Gap",
    "geometric_translate_y": "Geometric translate Y",
    "gift_card_form": "Gift card form",
    "gradient_direction": "Gradient direction",
    "heading": "Heading",
    "heading_preset": "Heading preset",
    "headings": "Headings",
    "height": "Height",
    "hide_logo_on_home_page": "Hide logo on home page",
    "hide_padding": "Hide padding",
    "hide_unselected_variant_media": "Hide unselected variant media",
    "horizontal_gap": "Horizontal gap",
    "horizontal_offset": "Shadow horizontal offset",
    "horizontal_padding": "Horizontal padding",
    "hover_background": "Hover background",
    "hover_behavior": "Hover behavior",
    "hover_borders": "Hover borders",
    "hover_text": "Hover text",
    "icon": "Icon",
    "icon_background": "Icon background",
    "icons": "Icons",
    "image": "Image",
    "image_border_radius": "Image corner radius",
    "image_gap": "Image gap",
    "image_icon": "Image icon",
    "image_opacity": "Image opacity",
    "image_position": "Image position",
    "image_ratio": "Image ratio",
    "inherit_color_scheme": "Inherit color scheme",
    "inventory_threshold": "Low stock threshold",
    "inverse": "Inverse",
    "inverse_logo": "Inverse logo",
    "installments": "Installments",
    "integrated_button": "Integrated button",
    "items_to_show": "Items to show",
    "label": "Label",
    "language_selector": "Language selector",
    "large": "Large",
    "layout": "Layout",
    "layout_gap": "Layout gap",
    "layout_style": "Style",
    "layout_type": "Type",
    "left": "Left",
    "left_padding": "Left padding",
    "length": "Length",
    "letter_spacing": "Letter spacing",
    "limit_content_width": "Limit content width",
    "limit_media_to_screen_height": "Constrain to screen height",
    "limit_product_details_width": "Limit product details width",
    "line_height": "Line height",
    "link": "Link",
    "link_preset": "Link preset",
    "links": "Links",
    "logo": "Logo",
    "logo_font": "Logo font",
    "loop": "Loop",
    "make_details_sticky_desktop": "Sticky on desktop",
    "make_section_full_width": "Make section full width",
    "max_width": "Max width",
    "media": "Media",
    "media_fit": "Media fit",
    "media_height": "Media height",
    "media_overlay": "Media overlay",
    "media_position": "Media position",
    "media_type": "Media type",
    "media_width": "Media width",
    "menu": "Menu",
    "minimum_height": "Minimum height",
    "mobile_card_size": "Mobile card size",
    "mobile_columns": "Mobile columns",
    "mobile_height": "Mobile height",
    "mobile_logo_image": "Mobile logo",
    "mobile_pagination": "Mobile pagination",
    "mobile_quick_add": "Mobile quick add",
    "motion": "Motion",
    "motion_direction": "Motion direction",
    "movement_direction": "Movement direction",
    "navigation": "Navigation",
    "navigation_bar": "Navigation bar",
    "navigation_bar_color_scheme": "Navigation bar color scheme",
    "opacity": "Opacity",
    "open_new_tab": "Open link in new tab",
    "open_row_by_default": "Open row by default",
    "overlay": "Overlay",
    "overlay_color": "Overlay color",
    "overlay_opacity": "Overlay opacity",
    "overlay_style": "Overlay style",
    "padding": "Padding",
    "padding_bottom": "Padding bottom",
    "padding_horizontal": "Padding horizontal",
    "padding_top": "Padding top",
    "page": "Page",
    "page_transition_enabled": "Page transition",
    "page_width": "Page width",
    "pagination": "Pagination",
    "percent_height": "Percent height",
    "percent_size": "Percent size",
    "percent_size_mobile": "Percent size",
    "percent_width": "Percent width",
    "pixel_size": "Pixel size",
    "pixel_size_mobile": "Pixel size",
    "placement": "Placement",
    "position": "Position",
    "preset": "Preset",
    "primary_button_background": "Primary button background",
    "primary_button_border": "Primary button border",
    "primary_button_text": "Primary button text",
    "primary_color": "Links",
    "primary_font": "Primary font",
    "primary_hover_color": "Hover links",
    "product": "Product",
    "product_and_card_title_case": "Product and card title case",
    "product_card_carousel": "Show carousel",
    "product_cards": "Product cards",
    "product_count": "Product count",
    "product_pages": "Product pages",
    "product_templates": "Product templates",
    "product_title_case": "Product title case",
    "product_type": "Product type",
    "products": "Products",
    "quick_add": "Quick add",
    "quick_add_colors": "Quick add colors",
    "ratio": "Ratio",
    "read_only": "Read only",
    "reflection_opacity": "Reflection opacity",
    "regular": "Regular",
    "review_count": "Review count",
    "right": "Right",
    "right_padding": "Right padding",
    "row": "Row",
    "scroll_speed": "Time to next announcement",
    "search": "Search",
    "search_icon": "Search icon",
    "search_position": "Position",
    "search_row": "Row",
    "row_height": "Row height",
    "secondary_button_background": "Secondary button background",
    "secondary_button_border": "Secondary button border",
    "secondary_button_text": "Secondary button text",
    "secondary_font": "Secondary font",
    "section_width": "Section width",
    "seller_note": "Allow note to seller",
    "shadow_color": "Shadow",
    "shadow_opacity": "Shadow opacity",
    "shape": "Shape",
    "show": "Show",
    "show_as_accordion": "Show as accordion on mobile",
    "show_author": "Author",
    "show_alignment": "Show alignment",
    "show_count": "Show count",
    "show_date": "Date",
    "show_filter_label": "Text labels for applied filters",
    "show_grid_layout_selector": "Show grid layout selector",
    "show_inventory_quantity": "Show low stock quantity",
    "show_pickup_availability": "Show pickup availability",
    "show_powered_by_shopify": "Show \"Powered by Shopify\"",
    "show_sale_price_first": "Show sale price first",
    "show_search": "Show search",
    "show_second_image_on_hover": "Show second image on hover",
    "show_swatch_label": "Text labels for swatches",
    "show_tax_info": "Tax information",
    "size": "Size",
    "size_mobile": "Mobile size",
    "slide_spacing": "Slide gap",
    "slide_width": "Slide width",
    "slideshow_fullwidth": "Full width slides",
    "small": "Small",
    "speed": "Speed",
    "statement": "Statement",
    "sticky_header": "Sticky header",
    "stroke": "Stroke",
    "style": "Style",
    "success_color": "Success",
    "swatches": "Swatches",
    "tertiary_font": "Tertiary font",
    "text": "Text",
    "text_case": "Case",
    "text_hierarchy": "Text hierarchy",
    "text_label_case": "Text label case",
    "text_presets": "Text presets",
    "thickness": "Thickness",
    "title": "Title",
    "top": "Top",
    "top_padding": "Top padding",
    "transition_to_main_product": "Product card to product page transition",
    "transparent_background": "Transparent background",
    "type": "Type",
    "type_preset": "Text preset",
    "underline_thickness": "Underline thickness",
    "unit": "Unit",
    "use_inverse_logo": "Use inverse logo",
    "variant_images": "Variant images",
    "vendor": "Vendor",
    "vertical_gap": "Vertical gap",
    "vertical_offset": "Shadow vertical offset",
    "vertical_on_mobile": "Vertical on mobile",
    "vertical_padding": "Vertical padding",
    "video": "Video",
    "video_alt_text": "Alt text",
    "video_autoplay": "Autoplay",
    "video_cover_image": "Cover image",
    "video_external_url": "URL",
    "video_loop": "Loop video",
    "video_position": "Video position",
    "video_source": "Source",
    "view_all_as_last_card": "\"View all\" as last card",
    "view_more_show": "Show View more button",
    "visibility": "Visibility",
    "weight": "Weight",
    "width": "Width",
    "width_desktop": "Desktop width",
    "width_mobile": "Mobile width",
    "wrap": "Wrap",
    "z_index": "Z-index",
    "product_corner_radius": "Product corner radius",
    "card_corner_radius": "Card corner radius"
  },
  "text_defaults": {
    "accordion_heading": "Accordion heading",
    "be_bold": "Be bold.",
    "button_label": "Shop now",
    "collapsible_row": "Collapsible row",
    "contact_form_button_label": "Submit",
    "email_signup_button_label": "Subscribe",
    "heading": "Heading",
    "popup_link": "Popup link",
    "sign_up": "Sign up",
    "shop_our_latest_arrivals": "Shop our latest arrivals!",
    "welcome_to_our_store": "Welcome to our store"
  }
}
