{"names": {"404": "404", "borders": "<PERSON><PERSON><PERSON>", "collapsible_row": "Einklappbare Reihe", "custom_section": "Benutzerdefinierter Abschnitt", "icon": "Symbol", "logo_and_favicon": "Logo und Favicon", "product_buy_buttons": "Kaufschaltflächen", "product_description": "Beschreibung", "product_price": "Pre<PERSON>", "slideshow": "Slideshow", "typography": "<PERSON><PERSON><PERSON><PERSON>", "video": "Video", "colors": "<PERSON><PERSON>", "overlapping_blocks": "Überlappende Blöcke", "product_variant_picker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slideshow_controls": "Slideshow-Steuerelemente", "size": "Größe", "spacing": "Abstand", "product_recommendations": "Empfohlene Produkte", "product_media": "Produktmedien", "featured_collection": "Vorgestellte Kollektion", "add_to_cart": "In den Warenkorb legen", "email_signup": "E-Mail-Anmeldung", "submit_button": "Schaltfläche „Senden“", "grid_layout_selector": "Raster-Layout-Auswahl", "image": "Bild", "list_items": "Listenelemente", "facets": "Filter", "variants": "<PERSON><PERSON><PERSON>", "styles": "Stile", "product_cards": "Produktkarten", "buttons": "Schaltflächen", "inputs": "Eingaben", "primary_button": "<PERSON><PERSON><PERSON><PERSON>", "secondary_button": "Sekundäre Schaltfläche", "popovers": "Pop-overs", "marquee": "Marquee", "products_carousel": "Vorgestellte Kollektion: Karussell", "products_grid": "Vorgestellte Kollektion: Raster", "pull_quote": "Zitat", "contact_form": "Kontaktformular", "featured_product": "Produkthighlight", "icons_with_text": "Symbole mit Text", "alternating_content_rows": "Abwechselnde Zeilen", "accelerated_checkout": "Beschleunigter Checkout", "accordion": "Akkordeon", "accordion_row": "Akkordeon-Zeile", "animations": "<PERSON><PERSON>", "announcement": "Ankündigung", "announcement_bar": "Ankündigungsleiste", "badges": "Badges", "button": "Schaltfläche", "cart": "<PERSON><PERSON><PERSON>", "cart_items": "Artikel im Warenkorb", "cart_products": "Produkte im Warenkorb", "cart_title": "<PERSON><PERSON><PERSON>", "collection": "Kollektion", "collection_card": "Kollektionskarte", "collection_columns": "Kollektionsspalten", "collection_container": "Kollektion", "collection_description": "Kollektionsbeschreibung", "collection_image": "Kollektionsbild", "collection_info": "Kollektionsinformationen", "collection_list": "Kollektionsliste", "collections": "Kollektionen", "collections_bento": "Kollektionsliste: <PERSON><PERSON>", "collections_carousel": "Kollektionsliste: <PERSON><PERSON><PERSON>", "collections_grid": "Kollektionsliste: Ra<PERSON>", "content": "Inhalt", "content_grid": "Inhaltsraster", "details": "Details", "divider": "Trennlinie", "divider_section": "Trennlinie", "faq_section": "FAQ", "filters": "Filtern und Sortieren", "follow_on_shop": "In Shop folgen", "footer": "Fußzeile", "footer_utilities": "Tools für Fußzeile", "group": "Gruppe", "header": "Header", "heading": "Überschrift", "hero": "Hero-B<PERSON>d", "icons": "Symbole", "image_with_text": "Bild mit Text", "input": "Eingabe", "logo": "Logo", "magazine_grid": "Ma<PERSON><PERSON><PERSON><PERSON>", "media": "Medien", "menu": "<PERSON><PERSON>", "mobile_layout": "Mobiles Layout", "payment_icons": "Zahlungssymbole", "popup_link": "Pop-up-<PERSON>", "predictive_search": "Popover suchen", "predictive_search_empty": "Vorausschauende <PERSON> leer", "price": "Pre<PERSON>", "product": "Produkt", "product_card": "Produktkarte", "product_card_media": "Medien", "product_card_rendering": "Rendering der Produktkarten", "product_grid": "<PERSON><PERSON>", "product_grid_main": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_image": "Produktbild", "product_information": "Produktinformationen", "product_review_stars": "Bewertungssterne", "quantity": "<PERSON><PERSON><PERSON>", "row": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected_variants": "Ausgewählte Varianten", "shop_the_look": "<PERSON> kaufen", "slide": "Folie", "social_media_links": "Social-Media-Links", "steps": "<PERSON><PERSON><PERSON>", "summary": "Übersicht", "swatches": "<PERSON><PERSON><PERSON><PERSON>", "testimonials": "Erfahrungsberichte", "text": "Text", "title": "Titel", "utilities": "Hilfsprogramme", "video_section": "Video", "spacer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_list": "Vorgestellte Kollektion", "jumbo_text": "Text im Großformat", "search_input": "Sucheingabe", "search_results": "Suchergebnisse", "read_only": "Schreibgeschützt", "collection_title": "Kollektionstitel", "view_all_button": "Alle anzeigen", "custom_liquid": "Benutzerdefiniertes Liquid", "blog": "Blog", "blog_post": "Blog-Beitrag", "blog_posts": "Blog-Beiträge", "caption": "Bildtext", "collection_card_image": "Bild", "collection_links": "Kollektions-Links", "collection_links_spotlight": "Kollektionslinks: Spotlight", "collection_links_text": "Kollektionslinks: Text", "collections_editorial": "Kollektionsliste: Editorial", "copyright": "Urheberrecht", "count": "<PERSON><PERSON><PERSON>", "drawers": "Einschübe", "editorial": "Editorial", "editorial_jumbo_text": "Editorial: Text im Großformat", "hero_marquee": "Hero: <PERSON><PERSON><PERSON>", "input_fields": "Eingabefelder", "local_pickup": "Lokale Abholung", "marquee_section": "Marquee", "media_with_text": "Medien mit Text", "page": "Seite", "page_content": "Inhalt", "page_layout": "Seitenlayout", "policy_list": "<PERSON><PERSON> <PERSON> Richtlinien", "prices": "<PERSON><PERSON>", "products_editorial": "Vorgestellte Kollektion: Redaktionell", "social_link": "Social-Media-Link", "split_showcase": "Split Showcase", "variant_pickers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product_title": "Produkttitel", "large_logo": "Großes Logo", "product_list_button": "Schaltfläche „Alle anzeigen“", "product_inventory": "Produktinventar", "pills": "<PERSON><PERSON>ln", "description": "Beschreibung", "featured_image": "Feature-Bild"}, "settings": {"autoplay": "Automatische Wiedergabe", "background": "Hi<PERSON>grund", "border_radius": "<PERSON><PERSON><PERSON><PERSON>", "border_width": "<PERSON><PERSON><PERSON>", "borders": "<PERSON><PERSON><PERSON>", "bottom_padding": "<PERSON><PERSON><PERSON> Padding", "color": "Farbe", "content_direction": "Inhaltsrichtung", "content_position": "Inhaltsposition", "cover_image_size": "Größe des Titelbilds", "cover_image": "Titelbild", "custom_width": "Benutzerdefinierte Breite", "enable_video_looping": "Videoschleife", "favicon": "Favicon", "heading": "Titel", "icon": "Symbol", "image_icon": "Bildsymbol", "make_section_full_width": "Abschnitt über die gesamte Breite", "overlay_opacity": "Überlagerungsdeckkraft", "padding": "Padding", "product": "Produkt", "text": "Text", "top_padding": "<PERSON><PERSON><PERSON>", "video": "Video", "video_alt_text": "Alt-Text", "video_loop": "Video in Dauerschleife", "video_position": "Videoposition", "width": "Breite", "alignment": "Ausrichtung", "button": "Schaltfläche", "colors": "<PERSON><PERSON>", "content_alignment": "Inhaltsausrichtung", "custom_minimum_height": "Benutzerdefinierte Mindesthöhe", "font_family": "Schriftartfamilie", "gap": "Abstand", "geometric_translate_y": "Geometric translate Y", "image": "Bild", "image_opacity": "Deckkraft des Bilds", "image_position": "Bildposition", "image_ratio": "Bildverhältnis", "label": "Beschriftung", "line_height": "Zeilenhöhe", "link": "Link", "layout_gap": "Lücke im Layout", "minimum_height": "Mindesthöhe", "opacity": "Opazität", "primary_color": "Links", "section_width": "Abschnittsbreite", "size": "Größe", "slide_spacing": "Lücke in der Folie", "slide_width": "Folienbreite", "slideshow_fullwidth": "Folien in voller Breite", "style": "Optik", "text_case": "Fall", "z_index": "z-index", "limit_content_width": "Begrenzte Inhaltsbreite", "color_scheme": "Farbschema", "inherit_color_scheme": "Farbschema übernehmen", "product_count": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_type": "Produkttyp", "content_width": "Inhaltsbreite", "collection": "Kollektion", "enable_sticky_content": "Fixierte Inhalte für Desktop", "error_color": "<PERSON><PERSON>", "success_color": "Erfolg", "primary_font": "<PERSON><PERSON><PERSON><PERSON>", "secondary_font": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tertiary_font": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columns": "Spalten", "items_to_show": "Anzuzeigende Elemente", "layout": "Layout", "layout_type": "Art", "show_grid_layout_selector": "Raster-Layout-Auswahl anzeigen", "view_more_show": "\"Mehr anzeigen\"-Schaltfläche anzeigen", "image_gap": "Bildabstand", "width_desktop": "Breite bei Desktop-Ansicht", "width_mobile": "Breite bei mobiler Ansicht", "border_style": "Stil für Rand", "height": "<PERSON><PERSON><PERSON>", "thickness": "<PERSON><PERSON>", "stroke": "<PERSON><PERSON>", "filter_style": "Filterstil", "swatches": "<PERSON><PERSON><PERSON><PERSON>", "quick_add_colors": "Farben schnell hinzufügen", "divider_color": "Trennlinie", "border_opacity": "Deckkraft des Rands", "hover_background": "Hover-Hintergrund", "hover_borders": "Hover-Grenzen", "hover_text": "Hover-Text", "primary_hover_color": "Hover-Links", "primary_button_text": "Text für primäre Schaltfläche", "primary_button_background": "Hintergrund für primäre Schaltfläche", "primary_button_border": "Grenze für primäre Schaltfläche", "secondary_button_text": "Text für sekundäre Schaltfläche", "secondary_button_background": "Hintergrund für sekundäre Schaltfläche", "secondary_button_border": "Grenze für sekundäre Schaltfläche", "shadow_color": "<PERSON><PERSON><PERSON>", "mobile_logo_image": "Mobiles Logo", "video_autoplay": "Automatische Wiedergabe", "video_cover_image": "Titelbild", "video_external_url": "URL", "video_source": "<PERSON><PERSON>", "card_image_height": "Produktbildhöhe", "first_row_media_position": "<PERSON>rste Zeile Medienposition", "accordion": "Akkordeon", "aspect_ratio": "Seitenverhältnis", "auto_rotate_announcements": "Autorotieren der Ankündigungen", "auto_rotate_slides": "Autorotieren der Slides", "badge_corner_radius": "<PERSON><PERSON><PERSON><PERSON>", "badge_position": "Position auf Karten", "badge_sale_color_scheme": "Sale", "badge_sold_out_color_scheme": "Ausverkauft", "behavior": "Verhalten", "blur": "Schatten-Weichzeichnung", "border": "Rand", "bottom": "Unten", "carousel_on_mobile": "<PERSON><PERSON><PERSON> in mobiler Ansicht", "cart_count": "Anzahl im Warenkorb", "cart_items": "Artikel im Warenkorb", "cart_related_products": "Ähnliche Produkte", "cart_title": "<PERSON><PERSON><PERSON>", "cart_total": "Gesamtbetrag im Warenkorb", "cart_type": "Art", "case": "Fall", "checkout_buttons": "Schaltflächen für beschleunigten Checkout", "collection_list": "Kollektionen", "collection_templates": "Kollektionsvorlagen", "content": "Inhalt", "corner_radius": "<PERSON><PERSON><PERSON><PERSON>", "country_region": "Land/Region", "currency_code": "Währungscode", "custom_height": "Benutzerdefinierte Höhe", "desktop_height": "Desktop-Höhe", "direction": "<PERSON><PERSON><PERSON>", "display": "Anzeige", "divider_thickness": "Dicke der Trennlinie", "divider": "Trennlinie", "dividers": "Trennlinien", "drop_shadow": "Schlagschatten", "empty_state_collection_info": "Wird vor der Eingabe einer Suche angezeigt", "empty_state_collection": "Kollektion mit Status „Leer“", "enable_filtering": "Filter", "enable_grid_density": "Steuerung des Raster-Layouts", "enable_sorting": "Sortierung", "enable_zoom": "Zoom aktivieren", "equal_columns": "Gle<PERSON>", "expand_first_group": "Erste Gruppe erweitern", "extend_media_to_screen_edge": "Medien bis zum Bildschirmrand erweitern", "extend_summary": "Bis zum Bildschirmrand erweitern", "extra_large": "Extra groß", "extra_small": "Extra klein", "flag": "<PERSON><PERSON><PERSON>", "font_price": "Schriftart für Preis", "font_weight": "Schriftstärke", "font": "<PERSON><PERSON><PERSON><PERSON>", "full_width_first_image": "Erstes Bild in voller Breite", "full_width_on_mobile": "Volle Breite in mobiler Ansicht", "heading_preset": "Überschrift (Voreinstellung)", "hide_unselected_variant_media": "Medien für nicht ausgewählte Variante ausblenden", "horizontal_gap": "Horizontale <PERSON>", "horizontal_offset": "Horizontaler Offset für Schatten", "hover_behavior": "Hover-Verhalten", "icon_background": "Symbol-Hintergrund", "icons": "Symbole", "image_border_radius": "<PERSON><PERSON><PERSON><PERSON>", "installments": "Raten", "integrated_button": "Integrierte Schaltfläche", "language_selector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON>", "left_padding": "Padding links", "left": "Links", "letter_spacing": "Buchstabenabstand", "limit_media_to_screen_height": "Auf Bildschirmhöhe beschränken", "limit_product_details_width": "Breite der Produktdetails beschränken", "link_preset": "Link-Voreinstellung", "links": "Links", "logo": "Logo", "loop": "<PERSON><PERSON><PERSON><PERSON>", "make_details_sticky_desktop": "Bei Desktop fixiert", "max_width": "Maximale Breite", "media_height": "Medienhöhe", "media_overlay": "Medienüberlagerung", "media_position": "Medienposition", "media_type": "Medientyp", "media_width": "Medienbreite", "menu": "<PERSON><PERSON>", "mobile_columns": "Spalten für mobile Ansicht", "mobile_height": "Höhe für mobile Ansicht", "mobile_quick_add": "Schnelles Hinzufügen für mobile Ansicht", "motion_direction": "Bewegungsrichtung", "motion": "Bewegung", "movement_direction": "Bewegungsrichtung", "navigation_bar_color_scheme": "Farbschema für Navigationsleiste", "navigation_bar": "Navigationsleiste", "navigation": "Navigation", "open_new_tab": "Link in neuem Tab öffnen", "overlay_color": "Überlagerungsfarbe", "overlay": "Überlagerung", "padding_bottom": "Padding unten", "padding_horizontal": "Padding horizontal", "padding_top": "Padding oben", "page_width": "Seitenbreite", "pagination": "Seitennummerierung", "placement": "Platzierung", "position": "Position", "preset": "Voreinstellung", "product_cards": "Produktkarten", "product_pages": "Produktseiten", "product_templates": "Produktvorlagen", "products": "Produkte", "quick_add": "Schnelles Hinzufügen", "ratio": "Verhältnis", "regular": "Regulär", "review_count": "Anzahl überprüfen", "right": "<PERSON><PERSON><PERSON>", "row_height": "Zeilenhöhe", "row": "<PERSON><PERSON><PERSON>", "seller_note": "Notiz für Verkäufer zulassen", "shape": "Form", "show_as_accordion": "Als Akkordeon in mobiler Ansicht anzeigen", "show_sale_price_first": "Verkaufspreis zuerst anzeigen", "show_tax_info": "Steuerinformationen", "show": "Anzeigen", "small": "<PERSON>", "speed": "Geschwindigkeit", "statement": "Aussage", "sticky_header": "<PERSON><PERSON><PERSON><PERSON>", "text_hierarchy": "Texthierarchie", "text_presets": "Textvoreinstellungen", "title": "Titel", "top": "<PERSON><PERSON>", "type_preset": "Textvoreinstellung", "type": "Art", "underline_thickness": "Unterstreichungsstärke", "variant_images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "vertical_gap": "<PERSON><PERSON><PERSON><PERSON>", "vertical_offset": "Vertikaler Offset für Schatten", "vertical_on_mobile": "Vertikal in mobiler Ansicht", "view_all_as_last_card": "„Alle anzeigen“ als letzte Karte", "weight": "Gewicht", "wrap": "Textumbruch", "logo_font": "Schriftart für Logo", "background_color": "Hintergrundfarbe", "size_mobile": "Mobile Größe", "pixel_size_mobile": "Größe in Pixel", "percent_size_mobile": "Größe in Prozent", "unit": "Einheit", "custom_mobile_size": "Benutzerdefinierte mobile Größe", "fixed_height": "Höhe in Pixeln", "fixed_width": "Breite in Pixeln", "percent_height": "Höhe in Prozent", "percent_width": "Breite in Prozent", "percent_size": "Größe in Prozent", "pixel_size": "Größe in Pixel", "hide_padding": "Padding ausblenden", "always_stack_buttons": "Schaltflächen immer stapeln", "custom_mobile_width": "Benutzerdefinierte mobile Breite", "shadow_opacity": "Deckkraft des Schattens", "show_filter_label": "Beschriftungen für angewandte Filter", "show_swatch_label": "Beschriftungen für Farbfelder", "transparent_background": "Transparenter Hintergrund", "read_only": "Schreibgeschützt", "gradient_direction": "<PERSON><PERSON><PERSON>", "headings": "Überschriften", "overlay_style": "Überlagerungsstil", "account": "Ko<PERSON>", "align_baseline": "Text-<PERSON><PERSON> ausrich<PERSON>", "add_discount_code": "Rabatte im Warenkorb erlauben", "background_overlay": "Hintergrund-Überlagerung", "background_media": "Medien im Hintergrund", "border_thickness": "<PERSON><PERSON><PERSON>", "bottom_row": "Unterste Zeile", "button_text_case": "Groß- oder Kleinschreibung", "button_text_weight": "Schriftstärke", "auto_open_cart_drawer": "Die Aktion „In den Warenkorb legen“ öffnet automatisch den Einschub", "collection_count": "Anzahl der Kollektionen", "custom_liquid": "Liquid-Code", "default": "Standard", "default_logo": "Standard-Logo", "divider_width": "Breite der Trennlinie", "hide_logo_on_home_page": "Logo auf der Startseite ausblenden", "horizontal_padding": "Horizontales Padding", "inverse": "Invertiert", "inverse_logo": "Invertiertes Logo", "layout_style": "Optik", "length": "<PERSON><PERSON><PERSON>", "mobile_pagination": "Mobile Seitennummerierung", "open_row_by_default": "<PERSON><PERSON>e <PERSON><PERSON><PERSON>", "page_transition_enabled": "Seitenübergang", "search": "<PERSON><PERSON>", "search_icon": "Suchsymbol", "search_position": "Position", "search_row": "<PERSON><PERSON><PERSON>", "show_author": "Autor", "show_alignment": "Ausrichtung anzeigen", "show_count": "<PERSON><PERSON><PERSON> anzeigen", "show_date": "Datum", "show_pickup_availability": "Verfügbarkeit von Abholungen anzeigen", "show_search": "<PERSON><PERSON> anzeigen", "use_inverse_logo": "Invertiertes Logo verwenden", "vertical_padding": "<PERSON><PERSON><PERSON><PERSON>", "visibility": "Sichtbarkeit", "product_corner_radius": "Produkt-<PERSON><PERSON><PERSON><PERSON>", "card_corner_radius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alignment_mobile": "Ausrichtung Mobilgerät", "animation_repeat": "Animation wiederholen", "blurred_reflection": "Unscharfe Spiegelung", "card_hover_effect": "Karten-Hover-Effekt", "card_size": "Kartengröße", "collection_title_case": "Groß-/Kleinschreibung des Kollektionstitels", "effects": "Effekte", "inventory_threshold": "Schwelle für niedrigen Lagerbestand", "mobile_card_size": "Kartengröße für Mobilgeräte", "page": "Seite", "product_and_card_title_case": "Groß-/Kleinschreibung des Produkt- und Kartentitels", "product_title_case": "Groß-/Kleinschreibung des Produkttitels", "reflection_opacity": "Opazität der Spiegelung", "right_padding": "<PERSON><PERSON><PERSON>", "show_inventory_quantity": "Menge bei niedrigem Lagerbestand anzeigen", "text_label_case": "Groß-/Kleinschreibung der Beschriftung", "transition_to_main_product": "<PERSON><PERSON><PERSON> von Produktkarte zu Produktseite", "show_second_image_on_hover": "Hover-Effekt mit zweitem Bild", "media": "Medien", "product_card_carousel": "<PERSON><PERSON><PERSON> anzeigen", "media_fit": "Medienanpassung", "scroll_speed": "Zeit bis zur nächsten Ankündigung", "show_powered_by_shopify": "\"Powered by Shopify\" anzeigen", "gift_card_form": "Gutscheinformular"}, "options": {"adapt_to_image": "An Bild anpassen", "apple": "<PERSON><PERSON><PERSON>", "arrow": "Pfeil", "banana": "<PERSON><PERSON>", "bottle": "Flasche", "box": "Box", "buttons": "Schaltflächen", "carrot": "<PERSON><PERSON><PERSON><PERSON>", "center": "<PERSON><PERSON><PERSON>", "chat_bubble": "Chat-Blase", "clipboard": "Zwischenablage", "contain": "Enthalten", "counter": "<PERSON><PERSON><PERSON>", "cover": "Cover", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dairy_free": "Laktosef<PERSON>i", "dairy": "<PERSON><PERSON><PERSON>", "dropdowns": "Dropdown-Listen", "dots": "Punkte", "dryer": "<PERSON><PERSON><PERSON>", "end": "<PERSON><PERSON>", "eye": "<PERSON><PERSON>", "facebook": "Facebook", "fire": "<PERSON><PERSON>", "gluten_free": "Glutenfrei", "heart": "<PERSON><PERSON>", "horizontal": "Horizontal", "instagram": "Instagram", "iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON>", "leaf": "<PERSON><PERSON>", "leather": "<PERSON><PERSON>", "lightning_bolt": "Blitz", "lipstick": "Lippenstift", "lock": "<PERSON><PERSON><PERSON>", "map_pin": "Pinnnadel", "medium": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON>", "numbers": "<PERSON><PERSON><PERSON>", "nut_free": "<PERSON><PERSON>", "pants": "<PERSON><PERSON>", "paw_print": "Pfotenabdruck", "pepper": "<PERSON><PERSON><PERSON>", "perfume": "Parfüm", "pinterest": "Pinterest", "plane": "Flugzeug", "plant": "<PERSON><PERSON><PERSON><PERSON>", "price_tag": "<PERSON><PERSON><PERSON>", "question_mark": "Fragezeichen", "recycle": "Wiederverwenden", "return": "Rückgabe", "ruler": "Lineal", "serving_dish": "Servierteller", "shirt": "<PERSON><PERSON><PERSON>", "shoe": "<PERSON><PERSON><PERSON>", "silhouette": "<PERSON><PERSON><PERSON><PERSON>", "small": "<PERSON>", "snapchat": "Snapchat", "snowflake": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "star": "Stern", "start": "Start", "stopwatch": "Stoppuhr", "tiktok": "TikTok", "truck": "Lieferwagen", "tumblr": "Tumblr", "twitter": "X (Twitter)", "vertical": "Vertikal", "vimeo": "Vimeo", "washing": "<PERSON><PERSON><PERSON>", "auto": "Auto", "default": "Standard", "fill": "Füllung", "fit": "Passform", "full": "Voll", "full_and_page": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt", "heading": "Titel", "landscape": "Querformat", "lg": "LG", "link": "Link", "lowercase": "Kleinschreibung", "m": "M", "outline": "<PERSON><PERSON><PERSON>", "page": "Seite", "portrait": "Hochformat", "s": "S", "sentence": "Satz", "solid": "Fest", "space_between": "Leerzeichen zwischen", "square": "Square", "uppercase": "Großbuchstaben", "circle": "Kreis", "swatches": "<PERSON><PERSON><PERSON><PERSON>", "full_and_page_offset_left": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt, Offset links", "full_and_page_offset_right": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt, Offset rechts", "offset_left": "Offset links", "offset_right": "Offset rechts", "page_center_aligned": "Seite, mittig ausgerichtet", "page_left_aligned": "Seite, links ausgerichtet", "page_right_aligned": "Seite, rechts ausgerichtet", "button": "Schaltfläche", "caption": "Bildtext", "h1": "Überschrift 1", "h2": "Überschrift 2", "h3": "Überschrift 3", "h4": "Überschrift 4", "h5": "Überschrift 5", "h6": "Überschrift 6", "paragraph": "Absatz", "primary": "<PERSON><PERSON><PERSON><PERSON>", "secondary": "Sekundär", "tertiary": "Tertiär", "chevron_left": "Chevron nach links", "chevron_right": "Chevron nach rechts", "diamond": "<PERSON><PERSON><PERSON>", "grid": "<PERSON><PERSON>", "parallelogram": "Parallelogramm", "rounded": "Gerundet", "fit_content": "Passform", "pills": "<PERSON><PERSON>ln", "heavy": "Kräftig", "thin": "<PERSON><PERSON><PERSON>", "drawer": "<PERSON><PERSON><PERSON>", "preview": "Vorschau", "text": "Text", "video_uploaded": "Hochgeladen", "video_external_url": "Externe URL", "aspect_ratio": "Seitenverhältnis", "above_carousel": "Oberhalb des Karussells", "all": "Alle", "always": "Immer", "arrows_large": "Große Pfeile", "arrows": "<PERSON><PERSON><PERSON>", "balance": "Balance", "bento": "<PERSON><PERSON>", "black": "<PERSON><PERSON><PERSON>", "bluesky": "<PERSON><PERSON>", "body_large": "Text (groß)", "body_regular": "Text (regu<PERSON><PERSON><PERSON>)", "body_small": "Text (klein)", "bold": "<PERSON><PERSON>", "bottom_left": "Unten, links", "bottom_right": "Unten, rechts", "bottom": "Unten", "capitalize": "In Großbuchstaben schreiben", "caret": "<PERSON><PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "check_box": "Kontrollkästchen", "chevron_large": "Große Chevrons", "chevron": "Chevron", "chevrons": "Chevrons", "classic": "Klassisch", "collection_images": "Kollektionsbilder", "color": "Farbe", "complementary": "Ergänzend", "dissolve": "Überblenden", "dotted": "Gepunktet", "editorial": "Editorial", "extra_large": "Extra groß", "extra_small": "Extra klein", "featured_collections": "Vorgestellte Kollektionen", "featured_products": "Vorgestellte Produkte", "font_primary": "<PERSON><PERSON><PERSON><PERSON>", "font_secondary": "Sekundär", "font_tertiary": "Tertiär", "forward": "Vorwärts", "full_screen": "Vollbild", "heading_extra_large": "Überschrift (extra groß)", "heading_extra_small": "Überschrift (extra klein)", "heading_large": "Überschrift (groß)", "heading_regular": "Überschrift (regulär)", "heading_small": "Überschrift (klein)", "icon": "Symbol", "image": "Bild", "input": "Eingabe", "inside_carousel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverse_large": "Invert<PERSON><PERSON> groß", "inverse": "Invertiert", "large_arrows": "Große Pfeile", "large_chevrons": "Große Chevrons", "left": "Links", "light": "Hell", "linkedin": "LinkedIn", "loose": "Ungebunden", "media_first": "Medien zu<PERSON>t", "media_second": "Medien als zweites", "modal": "Modal", "narrow": "<PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON>", "next_to_carousel": "<PERSON><PERSON><PERSON> dem <PERSON>ell", "normal": "Normal", "nowrap": "<PERSON><PERSON>", "off_media": "Nicht bei Medien", "on_media": "Bei Medien", "on_scroll_up": "<PERSON><PERSON>", "one_half": "1/2", "one_number": "1", "one_third": "1/3", "pill": "Oval", "plus": "Plus", "pretty": "<PERSON><PERSON><PERSON><PERSON>", "price": "Pre<PERSON>", "primary_style": "Primärer Stil", "rectangle": "<PERSON><PERSON><PERSON>", "regular": "Regulär", "related": "<PERSON><PERSON><PERSON>", "reverse": "Umkehren", "rich_text": "Rich Text", "right": "<PERSON><PERSON><PERSON>", "secondary_style": "Sekundärer Stil", "semibold": "<PERSON><PERSON><PERSON><PERSON>", "shaded": "<PERSON><PERSON><PERSON><PERSON>", "show_second_image": "Zweites Bild anzeigen", "single": "<PERSON><PERSON><PERSON>", "slide_left": "Nach links schieben", "slide_up": "Nach oben schieben", "spotify": "Spotify", "stack": "<PERSON><PERSON><PERSON>", "text_only": "Nur Text", "threads": "Threads", "thumbnails": "Vorschaubilder", "tight": "Eng", "top_left": "Oben links", "top_right": "<PERSON><PERSON> rechts", "top": "<PERSON><PERSON>", "two_number": "2", "two_thirds": "2/3", "underline": "Unterstreichen", "video": "Video", "wide": "Breit", "youtube": "YouTube", "up": "<PERSON><PERSON>", "down": "Unten", "gradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixed": "Fest", "pixel": "Pixel", "percent": "Prozent", "accent": "Akzent", "below_image": "Unter Bild", "body": "Nachricht", "button_primary": "<PERSON><PERSON><PERSON><PERSON>", "button_secondary": "Sekundäre Schaltfläche", "compact": "Kompakt", "crop_to_fit": "Passend zuschneiden", "hidden": "Versteckt", "hint": "<PERSON><PERSON><PERSON>", "maintain_aspect_ratio": "Seitenverhältnisse beibehalten", "off": "Aus", "on_image": "<PERSON><PERSON> Bild", "social_bluesky": "Social: <PERSON><PERSON>", "social_facebook": "Social: Facebook", "social_instagram": "Social: Instagram", "social_linkedin": "Social: LinkedIn", "social_pinterest": "Social: Pinterest", "social_snapchat": "Social: Snap<PERSON>t", "social_spotify": "Social: Spotify", "social_threads": "Social: Threads", "social_tiktok": "Social: TikTok", "social_tumblr": "Social: Tumblr", "social_twitter": "Social: X (Twitter)", "social_whatsapp": "Social: WhatsApp", "social_vimeo": "Social: <PERSON><PERSON><PERSON>", "social_youtube": "Social: YouTube", "spotlight": "Spotlight", "standard": "Standard", "subheading": "Unterüberschrift", "blur": "Weichzeichnen", "lift": "Lift", "reveal": "Einblenden", "scale": "Skalierung", "subtle_zoom": "Zoomen"}, "content": {"background_video": "Hintergrund-Video", "describe_the_video_for": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "width_is_automatically_optimized": "Breite wird automatisch für die mobile Nutzung optimiert.", "advanced": "Advanced", "background_image": "Hintergrundbild", "block_size": "Blockgröße", "borders": "<PERSON><PERSON><PERSON>", "section_size": "Abschnittsgröße", "slideshow_width": "Folienbreite", "typography": "<PERSON><PERSON><PERSON><PERSON>", "complementary_products": "Ergänzende Produkte müssen mit der Search & Discovery-App eingerichtet werden. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery)", "mobile_column_optimization": "Spalten werden automatisch für die mobile Nutzung optimiert", "content_width": "Inhaltsbreite wird nur angewendet, wenn für die Abschnittsbreite volle Breite festgelegt ist.", "adjustments_affect_all_content": "Gilt für alle Inhalte in diesem Block", "responsive_font_sizes": "Die Größe wird automatisch auf sämtliche Bildschirmgrößen skaliert.", "buttons": "Schaltflächen", "swatches": "<PERSON><PERSON><PERSON><PERSON>", "variant_settings": "Varianteneinstellungen", "background": "Hi<PERSON>grund", "appearance": "Erscheinungsbild", "arrows": "<PERSON><PERSON><PERSON>", "body_size": "Textgröße", "bottom_row_appearance": "Erscheinungsbild der untersten Zeile", "carousel_navigation": "Karussell-Navigation", "carousel_pagination": "Karussell-Seitennummerierung", "copyright": "Urheberrecht", "edit_logo_in_theme_settings": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo%20and%20favicon)", "edit_price_in_theme_settings": "Bearbeite die Preisformatierung in den [Theme-Einstellungen](/editor?context=theme&category=currency%20code)", "edit_variants_in_theme_settings": "Bearbeite den Variantenstil in den [Theme-Einstellungen](/editor?context=theme&category=variants)", "email_signups_create_customer_profiles": "Registrierungen hinzufügen [Kundenprofile ](https://help.shopify.com/manual/customers)", "follow_on_shop_eligiblity": "Damit die Schaltfläche angezeigt wird, muss der Shop-Kanal installiert und Shop Pay aktiviert sein. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)", "fonts": "Schriftarten", "grid": "<PERSON><PERSON>", "heading_size": "Größe der Überschrift", "image": "Bild", "input": "Eingabe", "layout": "Layout", "link": "Link", "link_padding": "Link-Padding", "localization": "Lokalisierung", "logo": "Logo", "margin": "Rand", "media": "Medien", "media_1": "Medien 1", "media_2": "Medien 2", "menu": "<PERSON><PERSON>", "mobile_layout": "Mobiles Layout", "padding": "Padding", "padding_desktop": "Desktop-Padding", "paragraph": "Absatz", "policies": "<PERSON><PERSON><PERSON><PERSON>", "popup": "Pop-up", "search": "<PERSON><PERSON>", "size": "Größe", "social_media": "Social Media", "submit_button": "Schaltfläche „Senden“", "text_presets": "Textvoreinstellungen", "transparent_background": "Transparenter Hintergrund", "typography_primary": "Prim<PERSON><PERSON> Typogra<PERSON>", "typography_secondary": "Sekundäre Typografie", "typography_tertiary": "Tertiäre Typografie", "mobile_size": "Mobile Größe", "cards_layout": "<PERSON><PERSON>-Layout", "section_layout": "Abschnitts-Layout", "mobile_width": "Breite bei mobiler Ansicht", "width": "Breite", "carousel": "<PERSON><PERSON><PERSON>", "colors": "<PERSON><PERSON>", "collection_page": "Kollektionsseite", "copyright_info": "Hier erfährst du, wie du [deinen Urheberrechtshinweis bearbeitest](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)", "customer_account": "Kundenkonto", "edit_empty_state_collection_in_theme_settings": "Kollektion mit Status „Leer“ in [Theme-Einstellungen]](/editor?context=theme&category=search) verwalten", "home_page": "Startseite", "images": "Bilder", "inverse_logo_info": "Wird ver<PERSON><PERSON>, wenn der transparente Header-Hintergrund auf „Invertiert“ gesetzt ist", "manage_customer_accounts": "[Sichtbarkeit verwalten](/admin/settings/customer_accounts) in den Kundenkontoeinstellungen. Veraltete Konten werden nicht unterstützt.", "manage_policies": "[<PERSON><PERSON><PERSON><PERSON> verwalten](/admin/settings/legal)", "product_page": "Produktseite", "text": "Text", "thumbnails": "Vorschaubilder", "visibility": "Sichtbarkeit", "visible_if_collection_has_more_products": "<PERSON><PERSON><PERSON>, wenn die Kollektion mehr als die angezeigten Produkte enthält", "grid_layout": "<PERSON><PERSON>-Layout", "app_required_for_ratings": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/apps)", "icon": "Symbol", "manage_store_name": "[Shop-<PERSON><PERSON> verwalten ](/admin/settings/general?edit=storeName)", "resource_reference_collection_card": "Zeigt Kollektion aus übergeordnetem Abschnitt an", "resource_reference_collection_card_image": "Zeigt Bild aus übergeordneter Kollektion an", "resource_reference_collection_title": "<PERSON>eigt <PERSON>l aus übergeordneter Kollektion an", "resource_reference_product": "Verbindet sich automatisch mit übergeordnetem Produkt", "resource_reference_product_card": "Zeigt Produkt aus übergeordnetem Abschnitt an", "resource_reference_product_inventory": "Zeigt Inventar aus übergeordnetem Produkt an", "resource_reference_product_media": "Zeigt Medien aus dem übergeordneten Produkt an", "resource_reference_product_price": "Zeigt Preis aus übergeordnetem Produkt an", "resource_reference_product_recommendations": "Zeigt Empfehlungen auf Basis des übergeordneten Produkts an", "resource_reference_product_review": "Zeigt Bewertungen aus übergeordnetem Produkt an", "resource_reference_product_swatches": "<PERSON><PERSON><PERSON> aus übergeordnetem Produkt an", "resource_reference_product_title": "<PERSON><PERSON>gt T<PERSON>l aus übergeordnetem Produkt an", "resource_reference_product_variant_picker": "<PERSON><PERSON><PERSON> aus übergeordnetem Produkt an", "product_media": "Produktmedien", "section_link": "Abschnitts-Link", "gift_card_form_description": "Kunden können Gutscheine per E-Mail mit einer persönlichen Nachricht an den Empfänger senden. [Mehr Informationen](https://help.shopify.com/manual/products/gift-card-products)"}, "html_defaults": {"share_information_about_your": "<p><PERSON><PERSON> Infos über deine Marke mit deinen Kunden. Beschreibe ein Produkt, kündige etwas an oder heiße Kunden willkommen.</p>"}, "text_defaults": {"collapsible_row": "Einklappbare Reihe", "button_label": "Jetzt einkaufen", "heading": "Titel", "email_signup_button_label": "Abonnieren", "accordion_heading": "Akkordeon-Überschrift", "contact_form_button_label": "Senden", "popup_link": "Pop-up-<PERSON>", "sign_up": "Registrieren", "welcome_to_our_store": "<PERSON><PERSON><PERSON><PERSON> in unserem Shop", "be_bold": "Sei mutig.", "shop_our_latest_arrivals": "Durchstöbere unsere aktuellsten Neuheiten!"}, "info": {"video_alt_text": "Beschreibe das Video für Nutzer von unterstützender Technologie.", "video_autoplay": "Videos werden standardmäßig stummgeschaltet", "video_external": "YouTube- oder Vimeo-URL verwenden", "carousel_layout_on_mobile": "<PERSON><PERSON><PERSON> wird auf Mobilgerät verwendet", "carousel_hover_behavior_not_supported": "„Karuss<PERSON>“-Hover wird nicht unterstützt, wenn der Typ „<PERSON><PERSON><PERSON>“ auf der Abschnittsebene ausgewählt ist", "link_info": "Optional: macht das Symbol anklickbar", "checkout_buttons": "Ermöglicht es Käufern, schneller auszuchecken, und kann die Conversion optimieren. [Mehr Informationen](https://help.shopify.com/manual/online-store/dynamic-checkout)", "custom_heading": "Benutzerdefinierte Überschrift", "edit_presets_in_theme_settings": "Bearbeite Voreinstellungen in den [Theme-Einstellungen](/editor?context=theme&category=typography)", "enable_filtering_info": "<PERSON>e Filter an mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)", "grid_layout_on_mobile": "Das Raster-Layout wird für die mobile Ansicht verwendet", "manage_countries_regions": "[<PERSON>änder/Regionen verwalten](/admin/settings/markets)", "manage_languages": "[<PERSON><PERSON><PERSON> verwalten](/admin/settings/languages)", "transparent_background": "Überprüfe alle Vorlagen, in denen ein transparenter Hintergrund verwendet wird, auf ihre Lesbarkeit", "logo_font": "<PERSON>t nur, wenn kein Logo ausgewählt wurde", "aspect_ratio_adjusted": "In einigen Layouts angepasst", "auto_open_cart_drawer": "Wenn er aktiviert ist, wird der Warenkorbeinschub automatisch geöffnet, wenn ein Produkt zum Warenkorb hinzugefügt wird.", "custom_liquid": "Füge App-Snippets oder anderen Code hinzu, um fortgeschrittene Anpassungen zu erstellen. [Mehr Informationen](https://shopify.dev/docs/api/liquid)", "applies_on_image_only": "Gilt nur für Bilder", "hover_effects": "Bezieht sich auf Produkt- und Kollektionskarten", "pills_usage": "<PERSON><PERSON><PERSON> an<PERSON>wan<PERSON>, Rabattcodes und Suchvorschläge"}, "categories": {"product_list": "Vorgestellte Kollektion", "basic": "Basic", "collection": "Kollektion", "collection_list": "Kollektionsliste", "footer": "Fußzeile", "forms": "Formulare", "header": "Header", "layout": "Layout", "links": "Links", "product": "Produkt", "banners": "Banner", "collections": "Kollektionen", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decorative": "<PERSON><PERSON><PERSON><PERSON>", "products": "Produkte", "other_sections": "<PERSON><PERSON><PERSON>", "storytelling": "Storytelling"}}