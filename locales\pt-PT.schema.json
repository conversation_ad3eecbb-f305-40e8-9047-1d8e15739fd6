{"names": {"404": "404", "borders": "Limites", "collapsible_row": "<PERSON><PERSON> re<PERSON>", "colors": "Cores", "custom_section": "Secção personalizada", "icon": "Ícone", "logo_and_favicon": "Logótipo e favicon", "overlapping_blocks": "Blocos sobrepostos", "product_buy_buttons": "Botõ<PERSON> de compra", "product_description": "Descrição", "product_price": "Preço", "product_variant_picker": "Se<PERSON>or de variante", "slideshow": "Apresentação de diapositivos", "typography": "Tipografia", "video": "Vídeo", "slideshow_controls": "Controlos da apresentação de slides", "size": "<PERSON><PERSON><PERSON>", "spacing": "Espaçamento", "product_recommendations": "<PERSON><PERSON><PERSON> recomendados", "product_media": "Conteúdo multimédia do produto", "featured_collection": "Coleção em destaque", "add_to_cart": "Adicionar ao car<PERSON>ho", "email_signup": "Registo de e-mail", "submit_button": "Botão Submeter", "grid_layout_selector": "<PERSON><PERSON><PERSON> <PERSON>", "image": "Imagem", "list_items": "<PERSON><PERSON> da lista", "facets": "Facetas", "variants": "<PERSON><PERSON><PERSON>", "styles": "Estilos", "product_cards": "Cartões de produtos", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "inputs": "Entradas", "primary_button": "Botão principal", "secondary_button": "Botão secundário", "popovers": "Pop-overs", "marquee": "Marcador", "pull_quote": "Obter orçamento", "contact_form": "Formulário de contacto", "featured_product": "Destaques do produto", "icons_with_text": "Ícones com texto", "alternating_content_rows": "<PERSON><PERSON>", "product_list": "Coleção em destaque", "spacer": "Espaçador", "accelerated_checkout": "Finalização da compra acelerada", "accordion": "Acordeão", "accordion_row": "<PERSON><PERSON>", "animations": "Animações", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "Barra de anúncio", "badges": "<PERSON><PERSON>", "button": "Botão", "cart": "<PERSON><PERSON><PERSON>", "cart_items": "Itens do carrinho", "cart_products": "Produtos do carrinho", "cart_title": "<PERSON><PERSON><PERSON>", "collection": "Coleção", "collection_card": "Cartão de coleção", "collection_columns": "Colunas de coleção", "collection_container": "Coleção", "collection_description": "Descrição de coleção", "collection_image": "Imagem da coleção", "collection_info": "Informação de coleção", "collection_list": "Lista de coleções", "collections": "Coleções", "content": "<PERSON><PERSON><PERSON><PERSON>", "content_grid": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "divider": "Divisor", "filters": "Filtragem e ordenação", "follow_on_shop": "Seguir na Shop", "footer": "Rodapé", "footer_utilities": "Utilitários de rodapé", "group": "Grupo", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON>", "icons": "Ícones", "image_with_text": "Imagem com texto", "input": "Entrada", "logo": "Logótipo", "magazine_grid": "<PERSON><PERSON><PERSON> revist<PERSON>", "media": "Conteúdo multimédia", "menu": "<PERSON><PERSON>", "mobile_layout": "Esquema para dispositivo móvel", "payment_icons": "Ícones de pagamento", "popup_link": "Ligação pop-up", "predictive_search": "Pesquisar pop-over", "predictive_search_empty": "Pesquisa preditiva vazia", "price": "Preço", "product": "Produ<PERSON>", "product_card": "Cartão de produto", "product_card_media": "Conteúdo multimédia", "product_card_rendering": "Renderização de cartão de produto", "product_grid": "Grelha", "product_grid_main": "<PERSON>rel<PERSON> de produtos", "product_image": "Imagem de produto", "product_information": "Informações de produto", "product_review_stars": "Estrelas de avaliação", "quantity": "Quantidade", "row": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "section": "Secção", "selected_variants": "Variantes selecionadas", "shop_the_look": "Visual Shop", "slide": "Diapositivo", "social_media_links": "Ligações de redes sociais", "steps": "Passos", "summary": "Resumo", "swatches": "Paletas", "testimonials": "<PERSON><PERSON><PERSON><PERSON>", "text": "Texto", "title": "<PERSON><PERSON><PERSON><PERSON>", "utilities": "Utilitários", "search_input": "Entrada de pesquisa", "search_results": "Resultados da pesquisa", "read_only": "Só de leitura", "collection_title": "<PERSON><PERSON><PERSON><PERSON> da coleção", "collections_bento": "Lista de coleções: <PERSON><PERSON>", "faq_section": "FAQ", "hero": "Hero", "jumbo_text": "Texto Ju<PERSON>", "view_all_button": "Ver tudo", "video_section": "Vídeo", "custom_liquid": "Liquid personalizado", "blog": "Blogue", "blog_post": "Publicação de blogue", "blog_posts": "Publicações no blogue", "caption": "<PERSON>a", "collection_card_image": "Imagem", "collection_links": "Ligações de coleção", "collection_links_spotlight": "Ligações de coleção: Spotlight", "collection_links_text": "Ligações de coleção: Texto", "collections_carousel": "Lista de coleções: Carrossel", "collections_editorial": "Lista de coleções: Editorial", "collections_grid": "Lista de coleções: Grelha", "copyright": "Direitos de autor", "count": "Contagem", "divider_section": "Divisor", "drawers": "Gavetas", "editorial": "Editorial", "editorial_jumbo_text": "Editorial: <PERSON><PERSON>", "hero_marquee": "Hero: <PERSON><PERSON>", "input_fields": "Campos de entrada", "local_pickup": "Recolha local", "marquee_section": "Marcador", "media_with_text": "Conteúdo multimédia com texto", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_content": "<PERSON><PERSON><PERSON><PERSON>", "page_layout": "Esquema da página", "policy_list": "Ligações para políticas", "prices": "Preços", "products_carousel": "Coleção em destaque: Carrossel", "products_editorial": "Coleção em destaque: Editorial", "products_grid": "Coleção em destaque: Grelha", "social_link": "Ligação para redes sociais", "split_showcase": "Apresentação dividida", "variant_pickers": "Seletores de variantes", "pills": "Forma de comprimidos", "product_title": "Título do produto", "large_logo": "Logótipo grande", "product_list_button": "Bot<PERSON> Ver tudo", "product_inventory": "Inventário de produtos", "description": "Descrição", "featured_image": "Imagem em destaque"}, "settings": {"alignment": "Alinhamento", "autoplay": "Reprodução automática", "background": "Fundo", "border_radius": "Raio do canto", "border_width": "Espessura do limite", "borders": "Limites", "bottom_padding": "Preenchimento inferior", "button": "Botão", "color": "Cor", "colors": "Cores", "content_alignment": "Alinhamento de conteúdo", "content_direction": "Direção do conteúdo", "content_position": "Posição do conteúdo", "cover_image_size": "<PERSON><PERSON><PERSON> de capa", "cover_image": "<PERSON><PERSON> de capa", "custom_minimum_height": "Altura mínima personalizada", "custom_width": "<PERSON>rgura <PERSON>", "enable_video_looping": "Loop de vídeo", "favicon": "Favicon", "font_family": "Família de tipo de letra", "gap": "Intervalo", "geometric_translate_y": "Tradução Y geométrica", "heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "Ícone", "image": "Imagem", "image_icon": "Ícone de imagem", "image_opacity": "Opacidade da imagem", "image_position": "Posição da imagem", "image_ratio": "Proporção de imagem", "label": "Etiqueta", "line_height": "Altura de linha", "link": "Ligação", "layout_gap": "Intervalo do esquema", "make_section_full_width": "Tornar a secção em largura total", "minimum_height": "<PERSON><PERSON> mínima", "opacity": "Opacidade", "overlay_opacity": "Opacidade de sobreposição", "padding": "Preenchimento", "primary_color": "Ligações", "product": "Produ<PERSON>", "section_width": "Largura da secção", "size": "<PERSON><PERSON><PERSON>", "slide_spacing": "Intervalo do diapositivo", "slide_width": "Largura do diapositivo", "slideshow_fullwidth": "Diapositivos de largura total", "style": "<PERSON><PERSON><PERSON>", "text": "Texto", "text_case": "Caixa", "top_padding": "Preenchimento superior", "video": "Vídeo", "video_alt_text": "Texto alternativo", "video_loop": "Repetir vídeo", "video_position": "Posição do vídeo", "width": "<PERSON><PERSON><PERSON>", "z_index": "<PERSON><PERSON><PERSON> Z", "limit_content_width": "Limitar largura do conteúdo", "color_scheme": "Esquema de cores", "inherit_color_scheme": "<PERSON><PERSON> esque<PERSON> de cores", "product_count": "Contagem de produtos", "product_type": "Tipo de produto", "content_width": "Largura do conteúdo", "collection": "Coleção", "enable_sticky_content": "Conteúdo fixador no ambiente de trabalho", "error_color": "Erro", "success_color": "Sucesso", "primary_font": "<PERSON><PERSON><PERSON> de <PERSON>ra principal", "secondary_font": "Tipo de letra secundário", "tertiary_font": "Tipo de letra terciário", "columns": "Colunas", "items_to_show": "Itens a apresentar", "layout": "Esquema", "layout_type": "Tipo", "show_grid_layout_selector": "<PERSON>rar seletor de esque<PERSON> de g<PERSON>", "view_more_show": "Mostrar o botão Ver mais", "image_gap": "Intervalo de imagem", "width_desktop": "Largura para computador", "width_mobile": "Largura para dispositivo móvel", "border_style": "<PERSON><PERSON><PERSON>", "height": "Altura", "thickness": "E<PERSON><PERSON><PERSON>", "stroke": "Traço", "filter_style": "Filtrar estilo", "swatches": "Paletas", "quick_add_colors": "Adição rápida de cores", "divider_color": "Divisor", "border_opacity": "Opacidade de borda", "hover_background": "Fundo ao passar o cursor", "hover_borders": "Contorno ao passar o cursor", "hover_text": "Texto ao passar o cursor", "primary_hover_color": "Ligação ao passar o cursor", "primary_button_text": "Texto do botão principal", "primary_button_background": "Fundo do botão principal", "primary_button_border": "Contorno do botão principal", "secondary_button_text": "Texto do botão secundário", "secondary_button_background": "Fundo do botão secundário", "secondary_button_border": "Contorno do botão secundário", "shadow_color": "Sombra", "limit_media_to_screen_height": "Ajustar à altura do ecrã", "mobile_logo_image": "Logótipo móvel", "video_autoplay": "Reprodução automática", "video_cover_image": "<PERSON><PERSON> de capa", "video_external_url": "URL", "video_source": "Fonte", "first_row_media_position": "Posição do conteúdo multimédia na primeira linha", "background_color": "Cor de fundo", "hide_padding": "Ocultar preenchimento", "logo_font": "Tipo de letra do logótipo", "size_mobile": "Taman<PERSON> para móvel", "pixel_size_mobile": "Tamanho em píxeis", "percent_size_mobile": "<PERSON><PERSON><PERSON> em percentagem", "unit": "Unidade", "custom_mobile_size": "Tamanho para móvel personalizado", "fixed_height": "Altura de píxel", "fixed_width": "Largura de píxel", "percent_height": "Altura em percentagem", "percent_width": "Largura em percentagem", "percent_size": "<PERSON><PERSON><PERSON> em percentagem", "pixel_size": "Tamanho em píxeis", "accordion": "Acordeão", "aspect_ratio": "Relação de altura/largura", "auto_rotate_announcements": "Rotação automática de anúncios", "auto_rotate_slides": "Rotação automática de diapositivos", "badge_corner_radius": "Raio do canto", "badge_position": "Posição nos cartões", "badge_sale_color_scheme": "<PERSON><PERSON><PERSON>", "badge_sold_out_color_scheme": "Esgotado", "behavior": "Comportamento", "blur": "Sombra desfocada", "border": "Limite", "bottom": "Inferior", "card_image_height": "Altura da imagem de produto", "carousel_on_mobile": "Carrossel em dispositivo móvel", "cart_count": "Contagem de carrinhos", "cart_items": "Itens do carrinho", "cart_related_products": "Produtos relacionados", "cart_title": "<PERSON><PERSON><PERSON>", "cart_total": "Total do carrinho", "cart_type": "Tipo", "case": "Caixa", "checkout_buttons": "Botões de finalização de compra acelerada", "collection_list": "Coleções", "collection_templates": "Modelos de coleção", "content": "<PERSON><PERSON><PERSON><PERSON>", "corner_radius": "Raio do canto", "country_region": "País/região", "currency_code": "<PERSON><PERSON><PERSON> da moeda", "custom_height": "Altura personalizada", "desktop_height": "Altura para computador", "direction": "Direção", "display": "Exibição", "divider_thickness": "Espessura do divisor", "divider": "Divisor", "dividers": "Divisores", "drop_shadow": "Sombra projetada", "empty_state_collection_info": "Apresentado antes da introdução de uma pesquisa", "empty_state_collection": "Coleção de estado vazio", "enable_filtering": "<PERSON><PERSON><PERSON>", "enable_grid_density": "Controlo de esquema de grelha", "enable_sorting": "Ordenação", "enable_zoom": "Ativar zoom", "equal_columns": "Colunas i<PERSON>", "expand_first_group": "Expandir primeiro grupo", "extend_media_to_screen_edge": "Estender conteúdo multimédia até à borda do ecrã", "extend_summary": "Estender até à borda do ecrã", "extra_large": "Extra grande", "extra_small": "Extra pequeno", "flag": "<PERSON><PERSON><PERSON>", "font_price": "Tipo de letra do preço", "font_weight": "Peso do tipo de letra", "font": "<PERSON><PERSON><PERSON> de letra", "full_width_first_image": "Largura total primeira imagem", "full_width_on_mobile": "Largura total em dispositivo móvel", "heading_preset": "Predefinição de título", "hide_unselected_variant_media": "Ocultar conteúdo multimédia de variante não selecionada", "horizontal_gap": "Intervalo horizontal", "horizontal_offset": "Desvio horizontal da sombra", "hover_behavior": "Comportamento ao passar o cursor", "icon_background": "Fundo do ícone", "icons": "Ícones", "image_border_radius": "Raio de canto da imagem", "installments": "Prestações", "integrated_button": "Botão integrado", "language_selector": "Se<PERSON>or de idioma", "large": "Grande", "left_padding": "Preenchi<PERSON> es<PERSON>do", "left": "E<PERSON>rda", "letter_spacing": "Espaçamento entre letras", "limit_product_details_width": "Limitar largura dos detalhes do produto", "link_preset": "Predefinição de ligação", "links": "Ligações", "logo": "Logótipo", "loop": "Repetição", "make_details_sticky_desktop": "Fixador para computador", "max_width": "<PERSON><PERSON><PERSON>", "media_height": "Altura do conteúdo multimédia", "media_overlay": "Sobreposição do conteúdo multimédia", "media_position": "Posição do conteúdo multimédia", "media_type": "Tipo de conteúdo multimédia", "media_width": "Largura do conteúdo multimédia", "menu": "<PERSON><PERSON>", "mobile_columns": "Colunas em dispositivos móveis", "mobile_height": "Altura em dispositivo móvel", "mobile_quick_add": "Adição rápida de dispositivo móvel", "motion_direction": "Direção de movimento", "motion": "Movimento", "movement_direction": "Direção de movimento", "navigation_bar_color_scheme": "Esquema de cores da barra de navegação", "navigation_bar": "Barra de navegação", "navigation": "Navegação", "open_new_tab": "Abrir ligação em novo separador", "overlay_color": "Cor de sobreposição", "overlay": "Sobreposição", "padding_bottom": "Preenchimento inferior", "padding_horizontal": "Preenchimento horizontal", "padding_top": "Preenchimento superior", "page_width": "<PERSON><PERSON><PERSON> da página", "pagination": "Paginação", "placement": "Posicionamento", "position": "Posição", "preset": "Predefinição", "product_cards": "Cartões de produtos", "product_pages": "Páginas de produtos", "product_templates": "Modelos de produto", "products": "<PERSON><PERSON><PERSON>", "quick_add": "<PERSON><PERSON><PERSON> rápida", "ratio": "Proporção", "regular": "Normal", "review_count": "Contagem de avaliações", "right": "<PERSON><PERSON><PERSON>", "row_height": "<PERSON>ura da linha", "row": "<PERSON><PERSON>", "seller_note": "<PERSON><PERSON><PERSON> nota ao vendedor", "shape": "Forma", "show_as_accordion": "Mostrar como acordeão em dispositivo móvel", "show_sale_price_first": "Mostrar preço de saldo primeiro", "show_tax_info": "Informações tributárias", "show": "Mostrar", "small": "Pequeno", "speed": "Velocidade", "statement": "Extrato", "sticky_header": "Cabeçalho fixo", "text_hierarchy": "Hierarquia de texto", "text_presets": "Predefinições de texto", "title": "<PERSON><PERSON><PERSON><PERSON>", "top": "Superior", "type": "Tipo", "type_preset": "Predefinição de texto", "underline_thickness": "Espessura do sublinhado", "variant_images": "Imagens da variante", "vendor": "Fornecedor", "vertical_gap": "Intervalo vertical", "vertical_offset": "Desvio vertical da sombra", "vertical_on_mobile": "Vertical para dispositivo móvel", "view_all_as_last_card": "\"Ver tudo\" como último cart<PERSON>", "weight": "Peso", "wrap": "Envolvimento", "read_only": "Só de leitura", "always_stack_buttons": "Empilhar sempre botões", "custom_mobile_width": "Largura para dispositivo móvel personalizada", "gradient_direction": "Direção de gradiente", "overlay_style": "Estilo da sobreposição", "shadow_opacity": "Opacidade da sombra", "show_filter_label": "Etiquetas de texto para filtros aplicados", "show_swatch_label": "Etiquetas de texto para paletas", "transparent_background": "Fundo transparente", "account": "Conta", "align_baseline": "<PERSON><PERSON><PERSON> baseline de texto", "add_discount_code": "Permitir descon<PERSON> no carrinho", "background_overlay": "Sobreposição de fundo", "background_media": "Conteúdo multimédia de fundo", "border_thickness": "Espessura do limite", "bottom_row": "Linha inferior", "button_text_case": "Texto em maiúsculas/minúsculas", "button_text_weight": "Peso do texto", "card_size": "Tamanho do <PERSON>ão", "auto_open_cart_drawer": "\"Adicionar ao carrinho\" abre automaticamente uma gaveta", "collection_count": "Número de coleções", "custom_liquid": "Código Liquid", "default": "Predefinição", "default_logo": "Logótipo pad<PERSON>", "divider_width": "Largura do divisor", "headings": "<PERSON><PERSON><PERSON><PERSON>", "hide_logo_on_home_page": "Ocultar logótipo na página inicial", "horizontal_padding": "Preenchimento horizontal", "inverse": "Invertido", "inverse_logo": "Logótipo invertido", "layout_style": "<PERSON><PERSON><PERSON>", "length": "Comprimento", "mobile_card_size": "Tamanho de cartão para dispositivo móvel", "mobile_pagination": "Paginação móvel", "open_row_by_default": "A<PERSON>r linha por predefinição", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_transition_enabled": "Transição de página", "right_padding": "Preenchimento direito", "search": "<PERSON><PERSON><PERSON><PERSON>", "search_icon": "Ícone de pesquisa", "search_position": "Posição", "search_row": "<PERSON><PERSON>", "show_author": "Autor", "show_alignment": "<PERSON><PERSON>", "show_count": "Mostrar contagem", "show_date": "Data", "show_pickup_availability": "Mostrar disponibilidade de recolha", "show_search": "Mostrar pesquisa", "use_inverse_logo": "Utilizar logótipo invertido", "vertical_padding": "Preenchimento vertical", "visibility": "Visibilidade", "product_corner_radius": "Raio de canto do produto", "card_corner_radius": "Raio de canto do cartão", "alignment_mobile": "Alinhamento em dispositivos móveis", "animation_repeat": "Repetir animação", "blurred_reflection": "Reflexo desfocado", "card_hover_effect": "Efeito ao passar o rato no cartão", "collection_title_case": "Formato de maiúsculas iniciais da coleção", "effects": "Efeitos", "inventory_threshold": "Limiar de stock baixo", "product_and_card_title_case": "Formato de maiúsculas iniciais do produto e cartão", "product_title_case": "Formato de maiúsculas iniciais do produto", "reflection_opacity": "Opacidade do reflexo", "show_inventory_quantity": "Mostrar quantidade de stock baixo", "text_label_case": "Formato de maiúsculas iniciais das etiquetas de texto", "transition_to_main_product": "Transição entre cartão de produto e página de produto", "show_second_image_on_hover": "Mostrar segunda imagem ao passar o rato", "media": "Conteúdo multimédia", "product_card_carousel": "<PERSON><PERSON> carrossel", "media_fit": "Ajuste do conteúdo multimédia", "scroll_speed": "Deslocar para o próximo anúncio", "show_powered_by_shopify": "Mostrar \"Powered by Shopify\"", "gift_card_form": "Formulário de cartão de oferta"}, "options": {"adapt_to_image": "Ada<PERSON>r à imagem", "apple": "Maçã", "arrow": "<PERSON><PERSON>", "auto": "Automático", "banana": "Banana", "bottle": "<PERSON><PERSON><PERSON>", "box": "Caixa", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "carrot": "<PERSON><PERSON><PERSON>", "center": "Centro", "chat_bubble": "<PERSON><PERSON><PERSON> de conversa", "clipboard": "Área de transferência", "contain": "<PERSON><PERSON><PERSON>", "counter": "<PERSON><PERSON><PERSON>", "cover": "Capa", "custom": "Personalizado", "dairy_free": "Sem produtos lácteos", "dairy": "<PERSON><PERSON><PERSON> l<PERSON>", "default": "Predefinição", "dropdowns": "Menus pendentes", "dots": "Pontos", "dryer": "Secador", "end": "Fim", "eye": "<PERSON><PERSON><PERSON>", "facebook": "Facebook", "fill": "<PERSON><PERSON><PERSON>", "fire": "Fogo", "fit": "Ajustar", "full": "Completo", "full_and_page": "<PERSON>o completo, conteúdo de largura de página", "gluten_free": "<PERSON><PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON>", "heart": "Coração", "horizontal": "Horizontal", "instagram": "Instagram", "iron": "<PERSON>rro", "landscape": "Horizontal", "large": "Grande", "leaf": "Fol<PERSON>", "leather": "<PERSON><PERSON>", "lg": "L", "lightning_bolt": "Relâmpago", "link": "Ligação", "lipstick": "<PERSON><PERSON>", "lock": "Cadeado", "lowercase": "minúsculas", "m": "M", "map_pin": "Marcador de mapa", "medium": "Médio", "none": "Nenhum(a)", "numbers": "Números", "nut_free": "Sem frutos de casca rija", "outline": "Contorno", "page": "<PERSON><PERSON><PERSON><PERSON>", "pants": "Calças", "paw_print": "<PERSON><PERSON> de <PERSON>a", "pepper": "Pimenta", "perfume": "Perfume", "pinterest": "Pinterest", "plane": "Avião", "plant": "Planta", "portrait": "Vertical", "price_tag": "Etiqueta de preço", "question_mark": "Ponto de interrogação", "recycle": "Reciclar", "return": "Devolução", "ruler": "Régua", "s": "S", "sentence": "<PERSON>ase", "serving_dish": "Prato", "shirt": "<PERSON><PERSON>", "shoe": "Sapato", "silhouette": "<PERSON><PERSON><PERSON><PERSON>", "small": "Pequeno", "snapchat": "Snapchat", "snowflake": "Floco de neve", "solid": "<PERSON><PERSON><PERSON><PERSON>", "space_between": "Espaço entre", "square": "Quadrado", "star": "Estrela", "start": "Início", "stopwatch": "Cronómetro", "tiktok": "TikTok", "truck": "Camião", "tumblr": "Tumblr", "twitter": "X (Twitter)", "uppercase": "Mai<PERSON><PERSON><PERSON>", "vertical": "Vertical", "vimeo": "Vimeo", "washing": "<PERSON><PERSON>", "circle": "<PERSON><PERSON><PERSON><PERSON>", "swatches": "Paletas", "full_and_page_offset_left": "<PERSON><PERSON> comple<PERSON>, conte<PERSON>do limitado à largura da página, ajustado à esquerda", "full_and_page_offset_right": "<PERSON><PERSON> comple<PERSON>, conte<PERSON>do limitado à largura da página, ajustado à direita", "offset_left": "Ajustado à esquerda", "offset_right": "Ajustado à direita", "page_center_aligned": "<PERSON><PERSON><PERSON><PERSON>, alinhado ao centro", "page_left_aligned": "<PERSON><PERSON><PERSON><PERSON>, al<PERSON><PERSON><PERSON> à esquerda", "page_right_aligned": "<PERSON><PERSON><PERSON><PERSON>, al<PERSON><PERSON>o à direita", "button": "Botão", "caption": "<PERSON>a", "h1": "Título 1", "h2": "Título 2", "h3": "Título 3", "h4": "Título 4", "h5": "Título 5", "h6": "Título 6", "paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "primary": "Principal", "secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tertiary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chevron_left": "Divisa para a esquerda", "chevron_right": "Divisa para a direita", "diamond": "Diamante", "grid": "Grelha", "parallelogram": "Paralelograma", "rounded": "<PERSON><PERSON><PERSON><PERSON>", "fit_content": "Ajustar", "pills": "Forma de comprimidos", "heavy": "Grosso", "thin": "Fino", "drawer": "Gaveta", "preview": "Pré-visualização", "text": "Texto", "video_uploaded": "Carregado", "video_external_url": "URL externo", "up": "Para cima", "down": "Para baixo", "gradient": "Gradiente", "aspect_ratio": "Relação de altura/largura", "fixed": "Fixo", "pixel": "<PERSON><PERSON><PERSON><PERSON>", "percent": "Percentagem", "above_carousel": "Por cima do carrossel", "all": "<PERSON><PERSON>", "always": "Sempre", "arrows_large": "Setas grandes", "arrows": "Set<PERSON>", "balance": "<PERSON><PERSON>", "bento": "<PERSON><PERSON>", "black": "Preto", "bluesky": "<PERSON><PERSON>", "body_large": "Corpo (Grande)", "body_regular": "Corpo (Normal)", "body_small": "Corpo (Pequeno)", "bold": "Negrito", "bottom_left": "Canto inferior esquerdo", "bottom_right": "Canto inferior direito", "bottom": "Inferior", "capitalize": "Capitalizar", "caret": "<PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "check_box": "Caixa de seleção", "chevron_large": "Divisas grandes", "chevron": "Divisa", "chevrons": "Divisas", "classic": "Clássico", "collection_images": "Imagens da coleção", "color": "Cor", "complementary": "Complementar", "dissolve": "Dissolver", "dotted": "Pontil<PERSON><PERSON>", "editorial": "Editorial", "extra_large": "Extra grande", "extra_small": "Extra pequeno", "featured_collections": "Coleções em destaque", "featured_products": "Produtos em destaque", "font_primary": "Principal", "font_secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "font_tertiary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forward": "<PERSON><PERSON><PERSON><PERSON>", "full_screen": "Ecrã inteiro", "heading_extra_large": "<PERSON><PERSON><PERSON><PERSON> (Extra grande)", "heading_extra_small": "<PERSON><PERSON><PERSON><PERSON> (Extra pequeno)", "heading_large": "<PERSON><PERSON><PERSON><PERSON> (Grande)", "heading_regular": "<PERSON><PERSON><PERSON><PERSON> (Normal)", "heading_small": "<PERSON><PERSON><PERSON><PERSON> (Pequeno)", "icon": "Ícone", "image": "Imagem", "input": "Entrada", "inside_carousel": "Carrossel interior", "inverse_large": "Invertido grande", "inverse": "Invertido", "large_arrows": "Setas grandes", "large_chevrons": "Divisas grandes", "left": "E<PERSON>rda", "light": "<PERSON><PERSON><PERSON>", "linkedin": "LinkedIn", "loose": "Solto", "media_first": "Conteúdo multimédia primeiro", "media_second": "Conteúdo multimédia segundo", "modal": "Modal", "narrow": "<PERSON><PERSON><PERSON><PERSON>", "never": "Nunca", "next_to_carousel": "<PERSON>to ao carrossel", "normal": "Normal", "nowrap": "<PERSON>m <PERSON><PERSON> de linha", "off_media": "Fora do conteúdo multimédia", "on_media": "No conteúdo multimédia", "on_scroll_up": "Ao rodar a roda do rato para cima", "one_half": "1/2", "one_number": "1", "one_third": "1/3", "pill": "Comprimido", "plus": "Plus", "pretty": "<PERSON><PERSON>", "price": "Preço", "primary_style": "<PERSON><PERSON><PERSON>", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "regular": "Normal", "related": "Relacionado", "reverse": "Inverter", "rich_text": "Texto formatado", "right": "<PERSON><PERSON><PERSON>", "secondary_style": "<PERSON><PERSON><PERSON>", "semibold": "Seminegrito", "shaded": "Sombreado", "show_second_image": "Mostrar segunda imagem", "single": "Único", "slide_left": "<PERSON><PERSON><PERSON> para a esquerda", "slide_up": "<PERSON><PERSON><PERSON> para cima", "spotify": "Spotify", "stack": "<PERSON><PERSON>", "text_only": "<PERSON>ó texto", "threads": "Threads", "thumbnails": "Miniaturas", "tight": "Apertado", "top_left": "Canto superior esquerdo", "top_right": "Canto superior direito", "top": "Superior", "two_number": "2", "two_thirds": "2/3", "underline": "<PERSON><PERSON><PERSON><PERSON>", "video": "Vídeo", "wide": "Largo", "youtube": "YouTube", "accent": "Destaque", "below_image": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "body": "Corpo", "button_primary": "Botão principal", "button_secondary": "Botão secundário", "compact": "Compacta", "crop_to_fit": "Recortar para ajustar", "hidden": "Oculto", "hint": "Dica", "maintain_aspect_ratio": "Manter proporção", "off": "Desativar", "on_image": "Na <PERSON>m", "social_bluesky": "Social: <PERSON><PERSON>", "social_facebook": "Social: Facebook", "social_instagram": "Social: Instagram", "social_linkedin": "Social: LinkedIn", "social_pinterest": "Social: Pinterest", "social_snapchat": "Social: Snap<PERSON>t", "social_spotify": "Social: Spotify", "social_threads": "Social: Threads", "social_tiktok": "Social: TikTok", "social_tumblr": "Social: Tumblr", "social_twitter": "Social: X (Twitter)", "social_whatsapp": "Social: WhatsApp", "social_vimeo": "Social: <PERSON><PERSON><PERSON>", "social_youtube": "Social: YouTube", "spotlight": "Spotlight", "standard": "Normal", "subheading": "Subtítulo", "blur": "Des<PERSON><PERSON>", "lift": "Elevar", "reveal": "<PERSON><PERSON><PERSON>", "scale": "Expandir", "subtle_zoom": "Zoom"}, "content": {"advanced": "Avançado", "background_image": "Imagem de fundo", "background_video": "Vídeo de fundo", "block_size": "Tamanho do bloco", "borders": "Limites", "describe_the_video_for": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [<PERSON>ber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "section_size": "<PERSON><PERSON><PERSON> da <PERSON>ção", "slideshow_width": "Largura do diapositivo", "typography": "Tipografia", "width_is_automatically_optimized": "A largura é otimizada automaticamente para dispositivos móveis.", "complementary_products": "Os produtos complementares têm de ser configurados utilizando a aplicação Search & Discovery. [Saber mais](https://help.shopify.com/manual/online-store/search-and-discovery)", "mobile_column_optimization": "As colunas serão otimizadas automaticamente para dispositivos móveis", "content_width": "Largura do conteúdo apenas aplicável se a largura da secção estiver definida para largura total.", "adjustments_affect_all_content": "Aplicável a todo o conteúdo neste bloco", "responsive_font_sizes": "Os tamanhos são dimensionados automaticamente para todos os tamanhos de ecrã", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "swatches": "Paletas", "variant_settings": "Definições de variante", "background": "Fundo", "cards_layout": "Esquema de cartões", "section_layout": "Esquema de secção", "mobile_size": "Taman<PERSON> para móvel", "appearance": "Aspeto", "arrows": "Set<PERSON>", "body_size": "Tamanho do corpo", "bottom_row_appearance": "Aspet<PERSON> da linha inferior", "carousel_navigation": "Navegação tipo carrossel", "carousel_pagination": "Paginação tipo carrossel", "copyright": "Direitos de autor", "edit_logo_in_theme_settings": "Editar logótipo em [definições de tema](/editor?context=theme&category=logo%20and%20favicon)", "edit_price_in_theme_settings": "Editar formatação de preço em [definições de tema](/editor?context=theme&category=currency%20code)", "edit_variants_in_theme_settings": "Editar estilo de variante em [definições de tema](/editor?context=theme&category=variants)", "email_signups_create_customer_profiles": "Adição de registos [perfis de clientes](https://help.shopify.com/manual/customers)", "follow_on_shop_eligiblity": "Para o botão aparecer, o canal Shop tem de ser instalado e o Shop Pay tem de ser ativado. [Saber mais](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)", "fonts": "Tipos de letra", "grid": "Grelha", "heading_size": "Tamanho do título", "image": "Imagem", "input": "Entrada", "layout": "Esquema", "link": "Ligação", "link_padding": "Preenchimento de ligação", "localization": "Localização", "logo": "Logótipo", "margin": "Margem", "media": "Conteúdo multimédia", "media_1": "Conteúdo multimédia 1", "media_2": "Conteúdo multimédia 2", "menu": "<PERSON><PERSON>", "mobile_layout": "Esquema para dispositivo móvel", "padding": "Preenchimento", "padding_desktop": "Preenchimento para computador", "paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "policies": "Políticas", "popup": "Pop-up", "search": "<PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "social_media": "Redes sociais", "submit_button": "Botão Submeter", "text_presets": "Predefinições de texto", "transparent_background": "Fundo transparente", "typography_primary": "Tipografia principal", "typography_secondary": "Tipografia secundária", "typography_tertiary": "Tipografia terciária", "mobile_width": "Largura para dispositivo móvel", "width": "<PERSON><PERSON><PERSON>", "carousel": "<PERSON><PERSON><PERSON>", "colors": "Cores", "collection_page": "Página de coleção", "copyright_info": "Saiba como [editar a sua declaração de direitos de autor](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)", "customer_account": "Conta de cliente", "edit_empty_state_collection_in_theme_settings": "Editar coleção de estado vazio em [definições de tema](/editor?context=theme&category=search)", "grid_layout": "Esquema de grelha", "home_page": "Página inicial", "images": "Imagens", "inverse_logo_info": "Utilizado quando o fundo transparente do cabeçalho é definido para Invertido", "manage_customer_accounts": "[G<PERSON>r visibilidade](/admin/settings/customer_accounts) nas definições de contas de cliente. As contas legadas não são suportadas.", "manage_policies": "[<PERSON><PERSON><PERSON>](/admin/settings/legal)", "product_page": "Página do produto", "text": "Texto", "thumbnails": "Miniaturas", "visibility": "Visibilidade", "visible_if_collection_has_more_products": "Visível se a coleção tiver mais produtos do que os mostrados", "app_required_for_ratings": "É necessária uma aplicação para as classificações de produto. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/apps)", "icon": "Ícone", "resource_reference_collection_card": "Apresenta a coleção da secção principal", "resource_reference_collection_card_image": "Apresenta a imagem da coleção principal", "resource_reference_collection_title": "Apresenta o título da coleção principal", "resource_reference_product": "Ligação automática ao produto principal", "resource_reference_product_card": "Apresenta o produto da secção principal", "resource_reference_product_inventory": "Apresenta o inventário do produto principal", "resource_reference_product_price": "Apresenta o preço do produto principal", "resource_reference_product_recommendations": "Apresenta recomendações com base no produto principal", "resource_reference_product_review": "Apresenta avaliações do produto principal", "resource_reference_product_swatches": "<PERSON><PERSON><PERSON> as paletas do produto principal", "resource_reference_product_title": "Apresenta o título do produto principal", "resource_reference_product_variant_picker": "Apresenta variantes do produto principal", "resource_reference_product_media": "Mostra o conteúdo multimédia do produto principal", "product_media": "Conteúdo multimédia do produto", "manage_store_name": "[<PERSON><PERSON><PERSON> o nome da loja](/admin/settings/general?edit=storeName)", "section_link": "Ligação para a secção", "gift_card_form_description": "Os clientes podem enviar cartões de oferta para o e-mail de um destinatário com uma mensagem pessoal. [<PERSON><PERSON> mais](https://help.shopify.com/manual/products/gift-card-products)"}, "html_defaults": {"share_information_about_your": "<p>Partilhe informações sobre a sua marca com os clientes. Descreva um produto, faça comunicados ou dê as boas-vindas aos clientes da loja.</p>"}, "text_defaults": {"button_label": "Comprar agora", "collapsible_row": "<PERSON><PERSON> re<PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON>", "email_signup_button_label": "Subscrever", "accordion_heading": "<PERSON><PERSON><PERSON><PERSON>", "contact_form_button_label": "Submeter", "popup_link": "Ligação pop-up", "sign_up": "Registe-se", "welcome_to_our_store": "Bem-vindo à nossa loja", "be_bold": "<PERSON><PERSON>.", "shop_our_latest_arrivals": "Compre as nossas novidades mais recentes!"}, "info": {"video_alt_text": "Descreva o vídeo para utilizadores de tecnologia de apoio", "video_autoplay": "O som dos vídeos estará desativado por predefinição", "video_external": "Utilizar um URL do YouTube ou Vimeo", "carousel_layout_on_mobile": "O carrossel é utilizado num dispositivo móvel", "link_info": "Opcional: torna o ícone clicável", "carousel_hover_behavior_not_supported": "Movimento \"Carrossel\" de passar o cursor não é suportado quando o tipo \"Carrossel\" é selecionado no nível de secção", "grid_layout_on_mobile": "O esquema de grelha é utilizado para dispositivos móveis", "logo_font": "Apenas aplicável quando não é selecionado um logótipo", "checkout_buttons": "Permite que os compradores finalizem a compra mais rapidamente e pode melhorar a conversão. [Saber mais](https://help.shopify.com/manual/online-store/dynamic-checkout)", "custom_heading": "<PERSON><PERSON><PERSON><PERSON>", "edit_presets_in_theme_settings": "Editar predefinições em [definições de tema](/editor?context=theme&category=typography)", "enable_filtering_info": "Personalizar filtros com a [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)", "manage_countries_regions": "[Gerir países/regiões](/admin/settings/markets)", "manage_languages": "[Gerir idiomas](/admin/settings/languages)", "transparent_background": "Reveja cada modelo em que é aplicado um fundo transparente para melhor legibilidade", "aspect_ratio_adjusted": "Ajustado em alguns esquemas", "auto_open_cart_drawer": "Quando ativado, o painel deslizante do carrinho abre-se automaticamente quando um produto é adicionado ao carrinho.", "custom_liquid": "Adicione fragmentos de aplicação ou outro código para criar personalizações avançadas. [Saber mais](https://shopify.dev/docs/api/liquid)", "pills_usage": "Utilizado para filtros aplicados, códigos de desconto e sugestões de pesquisa", "applies_on_image_only": "Apenas aplicável às imagens", "hover_effects": "Aplicável a cartões de produto e coleção"}, "categories": {"basic": "Basic", "collection": "Coleção", "collection_list": "Lista de coleções", "footer": "Rodapé", "forms": "Formulários", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout": "Esquema", "links": "Ligações", "product": "Produ<PERSON>", "product_list": "Coleção em destaque", "banners": "Faixas", "collections": "Coleções", "custom": "Personalizado", "decorative": "Decorativo", "products": "<PERSON><PERSON><PERSON>", "other_sections": "Outro", "storytelling": "Contar uma história"}}