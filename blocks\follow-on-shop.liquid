

{%- if shop.features.follow_on_shop? -%}
  <div
    class="spacing-style"
    style="{% render 'spacing-padding', settings: block.settings %}"
    {{ block.shopify_attributes }}
  >
    {{ shop | login_button: action: 'follow' }}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:names.follow_on_shop",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.follow_on_shop_eligiblity"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.follow_on_shop",
      "category": "t:categories.footer"
    }
  ]
}
{% endschema %}
