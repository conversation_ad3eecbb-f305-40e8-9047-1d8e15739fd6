{%- doc -%}
  Renders the account popover for desktop users, which appears when clicking the account icon. It contains links for account actions like login, logout, and viewing account details.

  @param {object} [settings] - The theme-level settings.

  @param {string} [settings.popover_color_scheme] - The color scheme for the popover panel.
{%- enddoc -%}
<anchored-popover-component
  data-close-on-resize="true"
  class="account-popover mobile:hidden"
>
  {% render 'account-button' with 'account-popover' as popover_id %}
  <div
    class="account-popover__panel details-content color-{{ settings.popover_color_scheme }}"
    id="account-popover"
    popover="auto"
    ref="popover"
  >
    {% render 'account-actions' %}
  </div>
</anchored-popover-component>

{% stylesheet %}
  .account-popover {
    --account-popover-min-width: 22rem;
    --account-actions-max-width: 22rem;
  }

  .account-popover__summary {
    padding: 0;

    &:hover {
      color: var(--color-foreground);
    }
  }

  .account-popover__panel {
    --account-popover-opacity: 0;
    --account-popover-y: 20px;
    border-radius: var(--style-border-radius-popover);
    margin: 0;
    top: calc(var(--anchor-top) * 1px + var(--minimum-touch-target) + var(--header-padding));
    left: unset;
    right: calc(var(--anchor-right) * 1px);

    width: max-content;
    min-width: var(--account-popover-min-width);
    box-shadow: var(--shadow-popover);
    border: var(--style-border-popover);
    background-color: var(--color-background);
    overflow-y: hidden;
    opacity: var(--account-popover-opacity);
    translate: 0 var(--account-popover-y);
    transition-property: display, opacity, translate;
    transition-duration: 0.3s;
    transition-timing-function: var(--ease-out-quad);
    transition-behavior: allow-discrete;

    &:popover-open {
      --account-popover-opacity: 1;
      --account-popover-y: 0px;
    }
  }

  @starting-style {
    .account-popover__panel {
      --account-popover-opacity: 0;
      --account-popover-y: 20px;
    }
    .account-popover__panel:popover-open {
      --account-popover-opacity: 0;
      --account-popover-y: 20px;
    }
  }
{% endstylesheet %}
