<!doctype html>
<html
  class="no-js{% if request.design_mode %} shopify-design-mode{% endif %}"
  lang="{{ request.locale.iso_code }}"
>
  <head>
	<!-- Added by AVADA SEO Suite -->
	{% include 'avada-seo' %}
	<!-- /Added by AVADA SEO Suite -->
    {%- render 'stylesheets' -%}

    {%- if settings.favicon != blank -%}
      <link
        rel="icon"
        type="image/png"
        href="{{ settings.favicon | image_url: width: 32, height: 32 }}"
      >
    {%- endif -%}

    {% comment %} This a way to wait for main content to load when navigating to a new page so that the view transitions can work consistently {% endcomment %}
    <link
      rel="expect"
      href="#MainContent"
      blocking="render"
      id="view-transition-render-blocker"
    >

    {%- render 'meta-tags' -%}
    {%- render 'fonts' -%}
    {%- render 'scripts' -%}
    {%- render 'theme-styles-variables' -%}
    {%- render 'color-schemes' -%}

    {% if request.design_mode %}
      {%- render 'theme-editor' -%}
    {% endif %}

    {{ content_for_header }}
  </head>

  <body class="page-width-{{ settings.page_width }} card-hover-effect-{{ settings.card_hover_effect }}">
    {% render 'skip-to-content-link', href: '#MainContent', text: 'accessibility.skip_to_text' %}
    <div id="header-group">
      {% sections 'header-group' %}
    </div>

    <script
      src="{{ 'critical.js' | asset_url }}"
      type="module"
      async
      blocking="render"
    ></script>

    <main
      id="MainContent"
      class="content-for-layout"
      role="main"
      data-page-transition-enabled="{{ settings.page_transition_enabled }}"
      data-product-transition="{{ settings.transition_to_main_product }}"
      data-template="{{ template }}"
    >
      <section-animation>
        {{ content_for_layout }}
      </section-animation>
    </main>

    {% sections 'footer-group' %}

    {% render 'search-modal' %}

    {% if settings.quick_add or settings.mobile_quick_add %}
      {% render 'quick-add-modal' %}
    {% endif %}
  </body>
</html>
